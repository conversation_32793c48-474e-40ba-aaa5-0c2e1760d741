<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemController</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_class">ItemController</span></div><h1>ItemController</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">315 of 1,054</td><td class="ctr2">70%</td><td class="bar">33 of 72</td><td class="ctr2">54%</td><td class="ctr1">37</td><td class="ctr2">66</td><td class="ctr1">72</td><td class="ctr2">248</td><td class="ctr1">7</td><td class="ctr2">27</td></tr></tfoot><tbody><tr><td id="a22"><a href="ItemController.java.html#L470" class="el_method">searchWithEs(String, int, int, Long, Double, Double, String, String, String, boolean)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="64" alt="64"/><img src="../jacoco-resources/greenbar.gif" width="71" height="10" title="94" alt="94"/></td><td class="ctr2" id="c16">59%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="5" alt="5"/></td><td class="ctr2" id="e7">35%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i4">23</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a8"><a href="ItemController.java.html#L81" class="el_method">getItems(int, int, String, String, String, Double, Double, String, Long)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="44" alt="44"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="67" alt="67"/></td><td class="ctr2" id="c15">60%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">28%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h0">12</td><td class="ctr2" id="i1">28</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a23"><a href="ItemController.java.html#L521" class="el_method">searchWithMySQL(String, int, int, Long, Double, Double, String, String, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="41" alt="41"/><img src="../jacoco-resources/greenbar.gif" width="37" height="10" title="49" alt="49"/></td><td class="ctr2" id="c18">54%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="3" alt="3"/></td><td class="ctr2" id="e8">33%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h1">12</td><td class="ctr2" id="i2">24</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="ItemController.java.html#L567" class="el_method">getCurrentUserId()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="32" alt="32"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="27" alt="27"/></td><td class="ctr2" id="c19">45%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h2">8</td><td class="ctr2" id="i6">17</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a26"><a href="ItemController.java.html#L205" class="el_method">updateItemStatus(Long, Map)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="21" alt="21"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="67" alt="67"/></td><td class="ctr2" id="c13">76%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="7" alt="7"/></td><td class="ctr2" id="e3">70%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i5">22</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a2"><a href="ItemController.java.html#L153" class="el_method">createItem(ItemRequest)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="19" alt="19"/><img src="../jacoco-resources/greenbar.gif" width="61" height="10" title="81" alt="81"/></td><td class="ctr2" id="c11">81%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="7" alt="7"/></td><td class="ctr2" id="e4">58%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g2">7</td><td class="ctr1" id="h5">6</td><td class="ctr2" id="i3">24</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="ItemController.java.html#L363" class="el_method">addComment(Long, Map)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="72" height="10" title="95" alt="95"/></td><td class="ctr2" id="c8">88%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="7" alt="7"/></td><td class="ctr2" id="e1">87%</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h6">5</td><td class="ctr2" id="i0">29</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a9"><a href="ItemController.java.html#L429" class="el_method">getMyItems(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="36" alt="36"/></td><td class="ctr2" id="c12">76%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="3" alt="3"/></td><td class="ctr2" id="e2">75%</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h7">4</td><td class="ctr2" id="i7">14</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="ItemController.java.html#L324" class="el_method">getItemComments(Long)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="27" alt="27"/></td><td class="ctr2" id="c14">71%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h8">4</td><td class="ctr2" id="i9">11</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a18"><a href="ItemController.java.html#L333" class="el_method">lambda$getItemComments$1(Comment)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="15" alt="15"/></td><td class="ctr2" id="c17">57%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="1" alt="1"/></td><td class="ctr2" id="e6">50%</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h9">4</td><td class="ctr2" id="i12">8</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="ItemController.java.html#L279" class="el_method">addToFavorites(Long)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="33" alt="33"/></td><td class="ctr2" id="c9">84%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i10">10</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a21"><a href="ItemController.java.html#L301" class="el_method">removeFromFavorites(Long)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="33" alt="33"/></td><td class="ctr2" id="c10">84%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">10</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a16"><a href="ItemController.java.html#L586" class="el_method">lambda$getCurrentUserId$1(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a15"><a href="ItemController.java.html#L579" class="el_method">lambda$getCurrentUserId$0(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a13"><a href="ItemController.java.html#L397" class="el_method">lambda$addComment$2()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a12"><a href="ItemController.java.html#L386" class="el_method">lambda$addComment$1()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a11"><a href="ItemController.java.html#L382" class="el_method">lambda$addComment$0()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a17"><a href="ItemController.java.html#L325" class="el_method">lambda$getItemComments$0()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a20"><a href="ItemController.java.html#L215" class="el_method">lambda$updateItemStatus$0()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a3"><a href="ItemController.java.html#L252" class="el_method">deleteItem(Long)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="43" alt="43"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d9"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i8">12</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a6"><a href="ItemController.java.html#L139" class="el_method">getItemById(Long)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="20" alt="20"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i14">4</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a10"><a href="ItemController.java.html#L60" class="el_method">ItemController(ItemService, UserRepository, ItemRepository, CommentRepository, ElasticsearchSyncService)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="18" alt="18"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i13">7</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a25"><a href="ItemController.java.html#L194" class="el_method">updateItem(Long, ItemRequest)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="11" alt="11"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a4"><a href="ItemController.java.html#L418" class="el_method">getAllItemsForDebug()</a></td><td class="bar" id="b23"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="9" alt="9"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">0</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a19"><a href="ItemController.java.html#L444" class="el_method">lambda$getMyItems$0(Item)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">0</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">0</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a14"><a href="ItemController.java.html#L256" class="el_method">lambda$deleteItem$0()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">0</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">0</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">0</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a24"><a href="ItemController.java.html#L51" class="el_method">static {...}</a></td><td class="bar" id="b26"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">0</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">0</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">0</td><td class="ctr2" id="k26">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>