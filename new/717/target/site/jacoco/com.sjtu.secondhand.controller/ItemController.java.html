<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">ItemController.java</span></div><h1>ItemController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.ItemRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.dto.response.CommentResponse;
import com.sjtu.secondhand.model.Category;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.model.Comment;
import com.sjtu.secondhand.repository.CategoryRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.repository.CommentRepository;
import com.sjtu.secondhand.service.FileStorageService;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.UserService;
import com.sjtu.secondhand.service.ElasticsearchSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping(&quot;/items&quot;)
@Tag(name = &quot;物品 (Items)&quot;, description = &quot;物品相关接口&quot;)
public class ItemController {

<span class="fc" id="L51">    private static final Logger logger = LoggerFactory.getLogger(ItemController.class);</span>

    private final ItemService itemService;
    private final UserRepository userRepository;
    private final ItemRepository itemRepository;
    private final CommentRepository commentRepository;
    private final ElasticsearchSyncService elasticsearchSyncService;

    public ItemController(ItemService itemService, UserRepository userRepository, ItemRepository itemRepository,
<span class="fc" id="L60">            CommentRepository commentRepository, ElasticsearchSyncService elasticsearchSyncService) {</span>
<span class="fc" id="L61">        this.itemService = itemService;</span>
<span class="fc" id="L62">        this.userRepository = userRepository;</span>
<span class="fc" id="L63">        this.itemRepository = itemRepository;</span>
<span class="fc" id="L64">        this.commentRepository = commentRepository;</span>
<span class="fc" id="L65">        this.elasticsearchSyncService = elasticsearchSyncService;</span>
<span class="fc" id="L66">    }</span>

    @GetMapping
    @Operation(summary = &quot;获取物品列表（支持筛选、排序和分页）&quot;, description = &quot;获取平台上的物品列表，可通过多种参数进行筛选、排序和分页&quot;)
    public ResponseEntity&lt;ApiResponse&lt;ItemPageResponse&gt;&gt; getItems(
            @RequestParam(value = &quot;page&quot;, defaultValue = &quot;1&quot;) int page,
            @RequestParam(value = &quot;size&quot;, defaultValue = &quot;20&quot;) int size,
            @RequestParam(value = &quot;sort&quot;, defaultValue = &quot;latest&quot;) String sort,
            @RequestParam(value = &quot;item_type&quot;, required = false) String itemType,
            @RequestParam(value = &quot;category_id&quot;, required = false) String categoryIdStr,
            @RequestParam(value = &quot;price_min&quot;, required = false) Double priceMin,
            @RequestParam(value = &quot;price_max&quot;, required = false) Double priceMax,
            @RequestParam(value = &quot;q&quot;, required = false) String keyword,
            @RequestParam(value = &quot;user_id&quot;, required = false) Long userId) {
        
<span class="fc" id="L81">        System.out.println(&quot;GET /items 接收参数: page=&quot; + page + &quot;, size=&quot; + size + &quot;, sort=&quot; + sort</span>
                + &quot;, item_type=&quot; + itemType + &quot;, category_id=&quot; + categoryIdStr
                + &quot;, price_min=&quot; + priceMin + &quot;, price_max=&quot; + priceMax
                + &quot;, q=&quot; + keyword + &quot;, user_id=&quot; + userId);
        
        // 将页码转换为从0开始，以符合Spring Data的Page接口
<span class="fc" id="L87">        page = page - 1;</span>
<span class="pc bpc" id="L88" title="1 of 2 branches missed.">        if (page &lt; 0) page = 0;</span>
        
        // 根据sort参数确定排序字段和方向
<span class="fc" id="L91">        String sortField = &quot;createdAt&quot;;</span>
<span class="fc" id="L92">        Sort.Direction direction = Sort.Direction.DESC;</span>
        
<span class="pc bpc" id="L94" title="4 of 5 branches missed.">        switch (sort.toLowerCase()) {</span>
            case &quot;latest&quot;:
<span class="fc" id="L96">                sortField = &quot;createdAt&quot;;</span>
<span class="fc" id="L97">                direction = Sort.Direction.DESC;</span>
<span class="fc" id="L98">                break;</span>
            case &quot;hot&quot;:
<span class="nc" id="L100">                sortField = &quot;viewCount&quot;;</span>
<span class="nc" id="L101">                direction = Sort.Direction.DESC;</span>
<span class="nc" id="L102">                break;</span>
            case &quot;price_asc&quot;:
<span class="nc" id="L104">                sortField = &quot;price&quot;;</span>
<span class="nc" id="L105">                direction = Sort.Direction.ASC;</span>
<span class="nc" id="L106">                break;</span>
            case &quot;price_desc&quot;:
<span class="nc" id="L108">                sortField = &quot;price&quot;;</span>
<span class="nc" id="L109">                direction = Sort.Direction.DESC;</span>
                break;
        }

<span class="fc" id="L113">        Sort sortObj = Sort.by(direction, sortField);</span>
<span class="fc" id="L114">        Pageable pageable = PageRequest.of(page, size, sortObj);</span>

        try {
            // 调用服务层方法，传递所有筛选条件
<span class="nc" id="L118">            Page&lt;Item&gt; itemPage = itemService.getFilteredItems(</span>
                    itemType, categoryIdStr, priceMin, priceMax, keyword, userId, pageable);

            // 使用新的批量处理方法设置收藏状态
<span class="nc" id="L122">            List&lt;ItemResponse&gt; itemResponses = itemService.convertItemsWithFavoriteStatus(itemPage.getContent());</span>

<span class="nc" id="L124">            ItemPageResponse response = new ItemPageResponse(itemResponses, itemPage);</span>

            // 返回符合测试期望格式的响应
<span class="nc" id="L127">            return ResponseEntity.ok(ApiResponse.success(&quot;操作成功&quot;, response));</span>
<span class="fc" id="L128">        } catch (Exception e) {</span>
<span class="fc" id="L129">            System.err.println(&quot;获取物品列表控制器异常: &quot; + e.getMessage());</span>
<span class="fc" id="L130">            e.printStackTrace(); // 打印完整堆栈信息</span>
<span class="fc" id="L131">            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)</span>
<span class="fc" id="L132">                    .body(ApiResponse.error(&quot;500 INTERNAL_SERVER_ERROR&quot;, &quot;获取物品列表失败: &quot; + e.getMessage()));</span>
        }
    }

    @GetMapping(&quot;/{id}&quot;)
    @Operation(summary = &quot;获取物品详情&quot;, description = &quot;根据ID获取单个物品的完整详细信息&quot;)
    public ResponseEntity&lt;ApiResponse&lt;ItemResponse&gt;&gt; getItemById(@PathVariable Long id) {
<span class="fc" id="L139">        System.out.println(&quot;【收藏量调试】控制器接收到获取物品详情请求，物品ID: &quot; + id);</span>
        
<span class="fc" id="L141">        ItemResponse itemResponse = itemService.getItemById(id);</span>
        
<span class="fc" id="L143">        System.out.println(&quot;【收藏量调试】控制器获取到物品详情，物品ID: &quot; + id + &quot;，收藏量: &quot; + itemResponse.getFavoriteCount());</span>
        
<span class="fc" id="L145">        return ResponseEntity.ok(ApiResponse.success(&quot;操作成功&quot;, itemResponse));</span>
    }

    @PostMapping
    @PreAuthorize(&quot;hasRole('USER')&quot;)
    @Operation(summary = &quot;发布商品&quot;, description = &quot;发布新商品信息&quot;, security = @SecurityRequirement(name = &quot;bearerAuth&quot;))
    public ResponseEntity&lt;ApiResponse&lt;ItemResponse&gt;&gt; createItem(@Valid @RequestBody ItemRequest itemRequest) {
        try {
<span class="fc" id="L153">            System.out.println(&quot;接收到创建商品请求: &quot; + itemRequest.getName() + &quot;, 类型: &quot; + itemRequest.getItem_type());</span>
<span class="fc" id="L154">            System.out.println(&quot;商品分类ID: &quot; + itemRequest.getCategory_id() + &quot;, 状态: &quot; + itemRequest.getCondition());</span>
<span class="fc" id="L155">            System.out.println(&quot;图片URL: &quot;</span>
<span class="pc bpc" id="L156" title="1 of 2 branches missed.">                    + (itemRequest.getImage_urls() != null ? String.join(&quot;, &quot;, itemRequest.getImage_urls()) : &quot;无&quot;));</span>

            // 验证必要的字段
<span class="pc bpc" id="L159" title="2 of 4 branches missed.">            if (itemRequest.getName() == null || itemRequest.getName().isEmpty()) {</span>
<span class="nc" id="L160">                System.err.println(&quot;商品名称为空&quot;);</span>
<span class="nc" id="L161">                return ResponseEntity.status(HttpStatus.BAD_REQUEST)</span>
<span class="nc" id="L162">                        .body(ApiResponse.error(&quot;商品名称不能为空&quot;));</span>
            }

<span class="pc bpc" id="L165" title="1 of 2 branches missed.">            if (itemRequest.getCategory_id() == null) {</span>
<span class="nc" id="L166">                System.err.println(&quot;商品分类ID为空&quot;);</span>
<span class="nc" id="L167">                return ResponseEntity.status(HttpStatus.BAD_REQUEST)</span>
<span class="nc" id="L168">                        .body(ApiResponse.error(&quot;商品分类ID不能为空&quot;));</span>
            }

<span class="pc bpc" id="L171" title="1 of 4 branches missed.">            if (itemRequest.getCondition() == null || itemRequest.getCondition().isEmpty()) {</span>
<span class="fc" id="L172">                System.err.println(&quot;商品状态为空&quot;);</span>
<span class="fc" id="L173">                return ResponseEntity.status(HttpStatus.BAD_REQUEST)</span>
<span class="fc" id="L174">                        .body(ApiResponse.error(&quot;商品状态不能为空&quot;));</span>
            }

<span class="fc" id="L177">            ItemResponse createdItem = itemService.createItem(itemRequest);</span>
<span class="fc" id="L178">            System.out.println(&quot;商品创建成功，ID: &quot; + createdItem.getId());</span>
<span class="fc" id="L179">            return ResponseEntity.ok(ApiResponse.success(createdItem));</span>
<span class="fc" id="L180">        } catch (Exception e) {</span>
<span class="fc" id="L181">            System.err.println(&quot;创建商品失败: &quot; + e.getMessage());</span>
<span class="fc" id="L182">            e.printStackTrace();</span>
<span class="fc" id="L183">            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)</span>
<span class="fc" id="L184">                    .body(ApiResponse.error(&quot;创建商品失败: &quot; + e.getMessage()));</span>
        }
    }

    @PutMapping(&quot;/{id}&quot;)
    @Operation(summary = &quot;更新物品信息&quot;, description = &quot;物主可以更新自己发布的物品信息&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;ItemResponse&gt;&gt; updateItem(
            @PathVariable Long id,
            @Valid @RequestBody ItemRequest itemRequest) {
<span class="fc" id="L194">        ItemResponse updatedItem = itemService.updateItem(id, itemRequest);</span>
<span class="fc" id="L195">        return ResponseEntity.ok(ApiResponse.success(&quot;物品更新成功&quot;, updatedItem));</span>
    }

    @PatchMapping(&quot;/{id}/status&quot;)
    @Operation(summary = &quot;更新物品状态&quot;, description = &quot;更新物品的上架/下架状态&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;ItemResponse&gt;&gt; updateItemStatus(
            @PathVariable Long id,
            @RequestBody Map&lt;String, String&gt; statusData) {
        try {
<span class="fc" id="L205">            String status = statusData.get(&quot;status&quot;);</span>
<span class="pc bpc" id="L206" title="2 of 4 branches missed.">            if (status == null || status.isEmpty()) {</span>
<span class="nc" id="L207">                return ResponseEntity.badRequest().body(ApiResponse.error(&quot;状态不能为空&quot;));</span>
            }

            // 获取当前用户ID
<span class="fc" id="L211">            Long userId = getCurrentUserId();</span>

            // 获取物品
<span class="fc" id="L214">            Item item = itemRepository.findById(id)</span>
<span class="pc" id="L215">                    .orElseThrow(() -&gt; new RuntimeException(&quot;物品不存在&quot;));</span>

            // 验证物品所有者
<span class="fc bfc" id="L218" title="All 2 branches covered.">            if (!item.getUser().getId().equals(userId)) {</span>
<span class="fc" id="L219">                return ResponseEntity.status(HttpStatus.FORBIDDEN)</span>
<span class="fc" id="L220">                        .body(ApiResponse.error(&quot;您没有权限更新此物品&quot;));</span>
            }

            // 更新物品状态
<span class="fc bfc" id="L224" title="All 2 branches covered.">            if (&quot;listed&quot;.equals(status)) {</span>
<span class="fc" id="L225">                item.setIsVisible(true);</span>
<span class="pc bpc" id="L226" title="1 of 2 branches missed.">            } else if (&quot;unlisted&quot;.equals(status)) {</span>
<span class="nc" id="L227">                item.setIsVisible(false);</span>
            } else {
<span class="fc" id="L229">                return ResponseEntity.badRequest()</span>
<span class="fc" id="L230">                        .body(ApiResponse.error(&quot;无效的状态值，必须是 'listed' 或 'unlisted'&quot;));</span>
            }

            // 保存更新
<span class="fc" id="L234">            item = itemRepository.save(item);</span>

            // 返回更新后的物品
<span class="fc" id="L237">            ItemResponse itemResponse = new ItemResponse(item);</span>
<span class="fc" id="L238">            return ResponseEntity.ok(ApiResponse.success(&quot;物品状态更新成功&quot;, itemResponse));</span>
<span class="nc" id="L239">        } catch (Exception e) {</span>
<span class="nc" id="L240">            e.printStackTrace();</span>
<span class="nc" id="L241">            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)</span>
<span class="nc" id="L242">                    .body(ApiResponse.error(&quot;更新物品状态失败: &quot; + e.getMessage()));</span>
        }
    }

    @DeleteMapping(&quot;/{id}&quot;)
    @Operation(summary = &quot;删除物品&quot;, description = &quot;删除指定ID的物品&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Void&gt;&gt; deleteItem(@PathVariable Long id) {
        try {
            // 获取当前用户ID
<span class="fc" id="L252">            Long userId = getCurrentUserId();</span>

            // 获取物品
<span class="fc" id="L255">            Item item = itemRepository.findById(id)</span>
<span class="fc" id="L256">                    .orElseThrow(() -&gt; new RuntimeException(&quot;物品不存在&quot;));</span>

            // 验证物品所有者
<span class="fc bfc" id="L259" title="All 2 branches covered.">            if (!item.getUser().getId().equals(userId)) {</span>
<span class="fc" id="L260">                return ResponseEntity.status(HttpStatus.FORBIDDEN)</span>
<span class="fc" id="L261">                        .body(ApiResponse.error(&quot;您没有权限删除此物品&quot;));</span>
            }

            // 删除物品
<span class="fc" id="L265">            itemRepository.delete(item);</span>

<span class="fc" id="L267">            return ResponseEntity.ok(ApiResponse.success(&quot;物品删除成功&quot;, null));</span>
<span class="fc" id="L268">        } catch (Exception e) {</span>
<span class="fc" id="L269">            e.printStackTrace();</span>
<span class="fc" id="L270">            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)</span>
<span class="fc" id="L271">                    .body(ApiResponse.error(&quot;删除物品失败: &quot; + e.getMessage()));</span>
        }
    }

    @PostMapping(&quot;/{id}/favorite&quot;)
    @Operation(summary = &quot;收藏物品&quot;, description = &quot;将指定ID的物品添加到当前用户的收藏列表&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Void&gt;&gt; addToFavorites(@PathVariable(&quot;id&quot;) Long itemId) {
<span class="fc" id="L279">        System.out.println(&quot;【收藏量调试】控制器接收到收藏物品请求，物品ID: &quot; + itemId);</span>
        
<span class="fc" id="L281">        Long userId = getCurrentUserId();</span>
<span class="fc" id="L282">        System.out.println(&quot;【收藏量调试】当前用户ID: &quot; + userId);</span>
        
<span class="fc" id="L284">        itemService.addToFavorites(itemId, userId);</span>
        
        // 收藏操作后，立即获取物品详情，检查收藏量是否更新
        try {
<span class="fc" id="L288">            ItemResponse updatedItem = itemService.getItemById(itemId);</span>
<span class="fc" id="L289">            System.out.println(&quot;【收藏量调试】收藏操作后，物品ID: &quot; + itemId + &quot;，最新收藏量: &quot; + updatedItem.getFavoriteCount());</span>
<span class="nc" id="L290">        } catch (Exception e) {</span>
<span class="nc" id="L291">            System.out.println(&quot;【收藏量调试】获取更新后的物品详情失败: &quot; + e.getMessage());</span>
<span class="fc" id="L292">        }</span>
        
<span class="fc" id="L294">        return ResponseEntity.ok(ApiResponse.success(&quot;收藏成功&quot;, null));</span>
    }

    @DeleteMapping(&quot;/{id}/favorite&quot;)
    @Operation(summary = &quot;取消收藏物品&quot;, description = &quot;将指定ID的物品从当前用户的收藏列表中移除&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Void&gt;&gt; removeFromFavorites(@PathVariable(&quot;id&quot;) Long itemId) {
<span class="fc" id="L301">        System.out.println(&quot;【收藏量调试】控制器接收到取消收藏物品请求，物品ID: &quot; + itemId);</span>
        
<span class="fc" id="L303">        Long userId = getCurrentUserId();</span>
<span class="fc" id="L304">        System.out.println(&quot;【收藏量调试】当前用户ID: &quot; + userId);</span>
        
<span class="fc" id="L306">        itemService.removeFromFavorites(itemId, userId);</span>
        
        // 取消收藏操作后，立即获取物品详情，检查收藏量是否更新
        try {
<span class="fc" id="L310">            ItemResponse updatedItem = itemService.getItemById(itemId);</span>
<span class="fc" id="L311">            System.out.println(&quot;【收藏量调试】取消收藏操作后，物品ID: &quot; + itemId + &quot;，最新收藏量: &quot; + updatedItem.getFavoriteCount());</span>
<span class="nc" id="L312">        } catch (Exception e) {</span>
<span class="nc" id="L313">            System.out.println(&quot;【收藏量调试】获取更新后的物品详情失败: &quot; + e.getMessage());</span>
<span class="fc" id="L314">        }</span>
        
<span class="fc" id="L316">        return ResponseEntity.ok(ApiResponse.success(&quot;取消收藏成功&quot;, null));</span>
    }

    @GetMapping(&quot;/{id}/comments&quot;)
    @Operation(summary = &quot;获取物品的评论列表&quot;, description = &quot;获取指定物品下的所有公开评论，支持嵌套（盖楼）&quot;)
    public ResponseEntity&lt;ApiResponse&lt;List&lt;CommentResponse&gt;&gt;&gt; getItemComments(@PathVariable Long id) {
        try {
            // 获取物品
<span class="fc" id="L324">            Item item = itemRepository.findById(id)</span>
<span class="pc" id="L325">                    .orElseThrow(() -&gt; new RuntimeException(&quot;物品不存在&quot;));</span>

            // 获取该物品的所有顶级评论（没有父评论的评论）
<span class="fc" id="L328">            List&lt;Comment&gt; comments = commentRepository.findByItemAndParentIsNullOrderByCreatedAtDesc(item);</span>

            // 将评论转换为响应对象
<span class="fc" id="L331">            List&lt;CommentResponse&gt; commentResponses = comments.stream()</span>
<span class="fc" id="L332">                    .map(comment -&gt; {</span>
<span class="fc" id="L333">                        CommentResponse response = new CommentResponse(comment);</span>

                        // 获取评论的回复
<span class="fc" id="L336">                        List&lt;Comment&gt; replies = commentRepository.findByParentOrderByCreatedAtAsc(comment);</span>
<span class="pc bpc" id="L337" title="1 of 2 branches missed.">                        if (!replies.isEmpty()) {</span>
<span class="nc" id="L338">                            List&lt;CommentResponse&gt; replyResponses = replies.stream()</span>
<span class="nc" id="L339">                                    .map(CommentResponse::new)</span>
<span class="nc" id="L340">                                    .collect(Collectors.toList());</span>
<span class="nc" id="L341">                            response.setReplies(replyResponses);</span>
                        }

<span class="fc" id="L344">                        return response;</span>
                    })
<span class="fc" id="L346">                    .collect(Collectors.toList());</span>

<span class="fc" id="L348">            return ResponseEntity.ok(ApiResponse.success(&quot;获取评论成功&quot;, commentResponses));</span>
<span class="nc" id="L349">        } catch (Exception e) {</span>
<span class="nc" id="L350">            e.printStackTrace();</span>
<span class="nc" id="L351">            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)</span>
<span class="nc" id="L352">                    .body(ApiResponse.error(&quot;获取评论失败: &quot; + e.getMessage()));</span>
        }
    }

    @PostMapping(&quot;/{id}/comments&quot;)
    @Operation(summary = &quot;添加评论&quot;, description = &quot;为指定物品添加评论&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;CommentResponse&gt;&gt; addComment(
            @PathVariable Long id,
            @RequestBody Map&lt;String, Object&gt; commentData) {
        try {
<span class="fc" id="L363">            String content = (String) commentData.get(&quot;content&quot;);</span>
<span class="pc bpc" id="L364" title="1 of 4 branches missed.">            if (content == null || content.trim().isEmpty()) {</span>
<span class="fc" id="L365">                return ResponseEntity.badRequest().body(ApiResponse.error(&quot;评论内容不能为空&quot;));</span>
            }

            // 获取父评论ID（如果有）
<span class="fc" id="L369">            Long parentId = null;</span>
<span class="fc bfc" id="L370" title="All 2 branches covered.">            if (commentData.get(&quot;parent_id&quot;) != null) {</span>
                try {
<span class="fc" id="L372">                    parentId = Long.parseLong(commentData.get(&quot;parent_id&quot;).toString());</span>
<span class="nc" id="L373">                } catch (NumberFormatException e) {</span>
                    // 忽略无效的父评论ID
<span class="fc" id="L375">                }</span>
            }

            // 获取当前用户
<span class="fc" id="L379">            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="fc" id="L380">            String username = authentication.getName();</span>
<span class="fc" id="L381">            User user = userRepository.findByUsername(username)</span>
<span class="pc" id="L382">                    .orElseThrow(() -&gt; new RuntimeException(&quot;用户不存在&quot;));</span>

            // 获取物品
<span class="fc" id="L385">            Item item = itemRepository.findById(id)</span>
<span class="pc" id="L386">                    .orElseThrow(() -&gt; new RuntimeException(&quot;物品不存在&quot;));</span>

            // 创建评论对象
<span class="fc" id="L389">            Comment comment = new Comment();</span>
<span class="fc" id="L390">            comment.setItem(item);</span>
<span class="fc" id="L391">            comment.setUser(user);</span>
<span class="fc" id="L392">            comment.setContent(content.trim());</span>

            // 如果有父评论ID，设置父评论
<span class="fc bfc" id="L395" title="All 2 branches covered.">            if (parentId != null) {</span>
<span class="fc" id="L396">                Comment parent = commentRepository.findById(parentId)</span>
<span class="pc" id="L397">                        .orElseThrow(() -&gt; new RuntimeException(&quot;父评论不存在&quot;));</span>
<span class="fc" id="L398">                comment.setParent(parent);</span>
            }

            // 保存评论
<span class="fc" id="L402">            comment = commentRepository.save(comment);</span>

            // 创建响应对象
<span class="fc" id="L405">            CommentResponse response = new CommentResponse(comment);</span>

<span class="fc" id="L407">            return ResponseEntity.ok(ApiResponse.success(&quot;评论成功&quot;, response));</span>
<span class="nc" id="L408">        } catch (Exception e) {</span>
<span class="nc" id="L409">            e.printStackTrace();</span>
<span class="nc" id="L410">            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)</span>
<span class="nc" id="L411">                    .body(ApiResponse.error(&quot;添加评论失败: &quot; + e.getMessage()));</span>
        }
    }

    @GetMapping(&quot;/debug&quot;)
    @Operation(summary = &quot;调试端点 - 获取所有商品&quot;, description = &quot;用于调试，返回数据库中的所有商品&quot;)
    public ResponseEntity&lt;ApiResponse&lt;List&lt;Item&gt;&gt;&gt; getAllItemsForDebug() {
<span class="fc" id="L418">        List&lt;Item&gt; items = itemRepository.findAll();</span>
<span class="fc" id="L419">        return ResponseEntity.ok(ApiResponse.success(&quot;获取所有商品成功&quot;, items));</span>
    }

    @GetMapping(&quot;/my&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    @Operation(summary = &quot;获取当前用户发布的物品&quot;, description = &quot;获取当前登录用户发布的所有物品&quot;)
    public ResponseEntity&lt;ApiResponse&lt;List&lt;ItemResponse&gt;&gt;&gt; getMyItems(
            @RequestParam(value = &quot;item_type&quot;, required = false) String itemType) {
        try {
            // 获取当前用户ID
<span class="fc" id="L429">            Long userId = getCurrentUserId();</span>

            // 获取用户发布的物品
            List&lt;Item&gt; items;
<span class="pc bpc" id="L433" title="1 of 4 branches missed.">            if (itemType != null &amp;&amp; !itemType.isEmpty()) {</span>
                // 如果指定了物品类型，则根据类型筛选
<span class="fc" id="L435">                Item.ItemType type = Item.ItemType.valueOf(itemType);</span>
<span class="fc" id="L436">                items = itemRepository.findByUserIdAndItemType(userId, type);</span>
<span class="fc" id="L437">            } else {</span>
                // 否则获取所有物品
<span class="fc" id="L439">                items = itemRepository.findByUserId(userId);</span>
            }

            // 转换为响应对象
<span class="fc" id="L443">            List&lt;ItemResponse&gt; itemResponses = items.stream()</span>
<span class="fc" id="L444">                    .map(item -&gt; new ItemResponse(item))</span>
<span class="fc" id="L445">                    .collect(Collectors.toList());</span>

<span class="fc" id="L447">            return ResponseEntity.ok(ApiResponse.success(&quot;获取我的物品成功&quot;, itemResponses));</span>
<span class="nc" id="L448">        } catch (Exception e) {</span>
<span class="nc" id="L449">            e.printStackTrace();</span>
<span class="nc" id="L450">            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)</span>
<span class="nc" id="L451">                    .body(ApiResponse.error(&quot;获取我的物品失败: &quot; + e.getMessage()));</span>
        }
    }

    @GetMapping(&quot;/search/es&quot;)
    @Operation(summary = &quot;使用Elasticsearch搜索物品&quot;, description = &quot;通过Elasticsearch实现的智能搜索功能，支持同义词和相关性排序&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; searchWithEs(
            @RequestParam(value = &quot;q&quot;, required = false) String keyword,
            @RequestParam(value = &quot;page&quot;, defaultValue = &quot;0&quot;) int page,
            @RequestParam(value = &quot;size&quot;, defaultValue = &quot;10&quot;) int size,
            @RequestParam(value = &quot;category_id&quot;, required = false) Long categoryId,
            @RequestParam(value = &quot;price_min&quot;, required = false) Double priceMin,
            @RequestParam(value = &quot;price_max&quot;, required = false) Double priceMax,
            @RequestParam(value = &quot;item_type&quot;, required = false) String itemType,
            @RequestParam(value = &quot;condition&quot;, required = false) String condition,
            @RequestParam(value = &quot;sort&quot;, defaultValue = &quot;latest&quot;) String sort,
            @RequestParam(value = &quot;direct_es&quot;, defaultValue = &quot;true&quot;) boolean directEs) {
        try {
            // 记录请求参数
<span class="fc" id="L470">            logger.info(&quot;接收到ES搜索请求 - 参数: keyword={}, page={}, size={}, directEs={}, sort={}&quot;, </span>
<span class="fc" id="L471">                        keyword, page, size, directEs, sort);</span>
            
            Object result;
            
            // 根据directEs参数决定使用哪种搜索方式
<span class="fc bfc" id="L476" title="All 2 branches covered.">            if (directEs) {</span>
<span class="fc" id="L477">                logger.info(&quot;使用直接调用ES API的搜索方式 (direct_es=true)&quot;);</span>
<span class="fc" id="L478">                result = elasticsearchSyncService.directAdvancedSearch(</span>
                        keyword, categoryId, priceMin, priceMax, itemType, condition, page, size, sort);
<span class="pc bpc" id="L480" title="1 of 2 branches missed.">                logger.info(&quot;直接ES搜索完成，结果类型: {}&quot;, result != null ? result.getClass().getName() : &quot;null&quot;);</span>
            } else {
<span class="fc" id="L482">                logger.info(&quot;使用Spring Data Elasticsearch搜索方式 (direct_es=false)&quot;);</span>
<span class="nc" id="L483">                result = elasticsearchSyncService.advancedSearch(</span>
                        keyword, categoryId, priceMin, priceMax, itemType, condition, page, size, sort);
<span class="nc bnc" id="L485" title="All 2 branches missed.">                logger.info(&quot;标准ES搜索完成，结果类型: {}&quot;, result != null ? result.getClass().getName() : &quot;null&quot;);</span>
            }
            
            // 检查结果是否需要回退到MySQL搜索
<span class="pc bpc" id="L489" title="1 of 2 branches missed.">            if (result instanceof Map) {</span>
<span class="fc" id="L490">                Map&lt;String, Object&gt; resultMap = (Map&lt;String, Object&gt;) result;</span>
<span class="fc" id="L491">                String source = (String) resultMap.getOrDefault(&quot;source&quot;, &quot;&quot;);</span>
<span class="fc" id="L492">                logger.info(&quot;搜索结果source标记: {}&quot;, source);</span>
                
                // 如果结果中包含elasticsearch_direct标记，表示直接搜索成功
<span class="pc bpc" id="L495" title="1 of 2 branches missed.">                if (&quot;elasticsearch_direct&quot;.equals(source)) {</span>
<span class="fc" id="L496">                    logger.info(&quot;直接ES搜索成功，返回结果&quot;);</span>
<span class="fc" id="L497">                    return ResponseEntity.ok(ApiResponse.success(&quot;搜索成功&quot;, result));</span>
                }
                
                // 如果ES搜索结果标记为需要回退，则执行MySQL搜索
<span class="nc bnc" id="L501" title="All 4 branches missed.">                if (&quot;elasticsearch_fallback&quot;.equals(source) || &quot;elasticsearch_error&quot;.equals(source)) {</span>
<span class="nc" id="L502">                    logger.info(&quot;检测到Elasticsearch回退标记，切换到MySQL搜索&quot;);</span>
<span class="nc" id="L503">                    return searchWithMySQL(keyword, page, size, categoryId, priceMin, priceMax, itemType, condition, sort);</span>
                }
            }
            
<span class="nc" id="L507">            return ResponseEntity.ok(ApiResponse.success(&quot;搜索成功&quot;, result));</span>
<span class="fc" id="L508">        } catch (Exception e) {</span>
<span class="fc" id="L509">            logger.error(&quot;ES搜索失败，回退到MySQL搜索: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L510">            return searchWithMySQL(keyword, page, size, categoryId, priceMin, priceMax, itemType, condition, sort);</span>
        }
    }

    // 提取MySQL搜索方法
    private ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; searchWithMySQL(
            String keyword, int page, int size, Long categoryId, 
            Double priceMin, Double maxPrice, String itemType, 
            String condition, String sort) {
        
        // 将页码转换为从0开始，以符合Spring Data的Page接口
<span class="fc" id="L521">        int adjustedPage = page;</span>
<span class="pc bpc" id="L522" title="1 of 2 branches missed.">        if (page &gt; 0) adjustedPage = page - 1;</span>
        
        // 根据sort参数确定排序字段和方向
<span class="fc" id="L525">        String sortField = &quot;createdAt&quot;;</span>
<span class="fc" id="L526">        Sort.Direction direction = Sort.Direction.DESC;</span>
        
<span class="pc bpc" id="L528" title="4 of 5 branches missed.">        switch (sort.toLowerCase()) {</span>
            case &quot;latest&quot;:
<span class="fc" id="L530">                sortField = &quot;createdAt&quot;;</span>
<span class="fc" id="L531">                direction = Sort.Direction.DESC;</span>
<span class="fc" id="L532">                break;</span>
            case &quot;hot&quot;:
<span class="nc" id="L534">                sortField = &quot;viewCount&quot;;</span>
<span class="nc" id="L535">                direction = Sort.Direction.DESC;</span>
<span class="nc" id="L536">                break;</span>
            case &quot;price_asc&quot;:
<span class="nc" id="L538">                sortField = &quot;price&quot;;</span>
<span class="nc" id="L539">                direction = Sort.Direction.ASC;</span>
<span class="nc" id="L540">                break;</span>
            case &quot;price_desc&quot;:
<span class="nc" id="L542">                sortField = &quot;price&quot;;</span>
<span class="nc" id="L543">                direction = Sort.Direction.DESC;</span>
                break;
        }

<span class="fc" id="L547">        Sort sortObj = Sort.by(direction, sortField);</span>
<span class="fc" id="L548">        Pageable pageable = PageRequest.of(adjustedPage, size, sortObj);</span>

        // 调用服务层方法，传递所有筛选条件
<span class="fc" id="L551">        Page&lt;Item&gt; itemPage = itemService.getFilteredItems(</span>
<span class="pc bpc" id="L552" title="1 of 2 branches missed.">                itemType, categoryId != null ? categoryId.toString() : null, </span>
                priceMin, maxPrice, keyword, null, pageable);

        // 使用新的批量处理方法设置收藏状态
<span class="nc" id="L556">        List&lt;ItemResponse&gt; itemResponses = itemService.convertItemsWithFavoriteStatus(itemPage.getContent());</span>

<span class="nc" id="L558">        ItemPageResponse response = new ItemPageResponse(itemResponses, itemPage);</span>
<span class="nc" id="L559">        response.setSource(&quot;mysql&quot;); // 标记数据来源</span>

        // 返回符合测试期望格式的响应
<span class="nc" id="L562">        return ResponseEntity.ok(ApiResponse.success(&quot;操作成功&quot;, response));</span>
    }

    // 辅助方法：获取当前登录用户ID
    private Long getCurrentUserId() {
<span class="fc" id="L567">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="pc bpc" id="L568" title="1 of 2 branches missed.">        if (authentication != null) {</span>
            try {
                // 首先尝试从UserDetails中获取ID
<span class="pc bpc" id="L571" title="1 of 2 branches missed.">                if (authentication.getPrincipal() instanceof UserDetails) {</span>
<span class="fc" id="L572">                    UserDetails userDetails = (UserDetails) authentication.getPrincipal();</span>
                    try {
<span class="nc" id="L574">                        return Long.parseLong(userDetails.getUsername());</span>
<span class="fc" id="L575">                    } catch (NumberFormatException e) {</span>
                        // 如果用户名不是数字，则通过用户名查找用户
<span class="fc" id="L577">                        String username = userDetails.getUsername();</span>
<span class="fc" id="L578">                        return userRepository.findByUsername(username)</span>
<span class="pc" id="L579">                                .orElseThrow(() -&gt; new RuntimeException(&quot;用户不存在: &quot; + username))</span>
<span class="fc" id="L580">                                .getId();</span>
                    }
                } else {
                    // 如果Principal不是UserDetails，则尝试通过用户名查找用户
<span class="nc" id="L584">                    String username = authentication.getName();</span>
<span class="nc" id="L585">                    return userRepository.findByUsername(username)</span>
<span class="nc" id="L586">                            .orElseThrow(() -&gt; new RuntimeException(&quot;用户不存在: &quot; + username))</span>
<span class="nc" id="L587">                            .getId();</span>
                }
<span class="nc" id="L589">            } catch (Exception e) {</span>
<span class="nc" id="L590">                throw new RuntimeException(&quot;获取用户ID失败: &quot; + e.getMessage(), e);</span>
            }
        }
<span class="nc" id="L593">        throw new IllegalStateException(&quot;未登录用户&quot;);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>