<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ElasticsearchController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">ElasticsearchController.java</span></div><h1>ElasticsearchController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.service.ElasticsearchSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

@RestController
@RequestMapping(&quot;/es&quot;)
@Tag(name = &quot;全文搜索 (Elasticsearch)&quot;, description = &quot;基于Elasticsearch的智能搜索接口&quot;)
public class ElasticsearchController {

<span class="fc" id="L23">    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchController.class);</span>
    private final ElasticsearchSyncService elasticsearchSyncService;

    @Autowired
<span class="fc" id="L27">    public ElasticsearchController(ElasticsearchSyncService elasticsearchSyncService) {</span>
<span class="fc" id="L28">        this.elasticsearchSyncService = elasticsearchSyncService;</span>
<span class="fc" id="L29">    }</span>

    @PostMapping(&quot;/sync&quot;)
    @Operation(summary = &quot;同步所有物品到ES&quot;, description = &quot;将数据库中的所有物品数据同步到Elasticsearch&quot;)
    public ResponseEntity&lt;ApiResponse&lt;String&gt;&gt; syncAllItems() {
        try {
<span class="fc" id="L35">            elasticsearchSyncService.syncAllItemsToElasticsearch();</span>
<span class="fc" id="L36">            return ResponseEntity.ok(ApiResponse.success(&quot;同步所有物品到Elasticsearch成功&quot;));</span>
<span class="fc" id="L37">        } catch (Exception e) {</span>
<span class="fc" id="L38">            logger.error(&quot;同步物品到ES失败: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L39">            return ResponseEntity.status(500).body(ApiResponse.error(&quot;ES_SYNC_ERROR&quot;, &quot;同步物品到ES失败: &quot; + e.getMessage()));</span>
        }
    }

    @GetMapping(&quot;/search&quot;)
    @Operation(summary = &quot;通过关键词搜索物品&quot;, description = &quot;从ES中搜索物品，支持分页&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; search(
            @RequestParam(&quot;q&quot;) String keyword,
            @RequestParam(value = &quot;page&quot;, defaultValue = &quot;0&quot;) int page,
            @RequestParam(value = &quot;size&quot;, defaultValue = &quot;10&quot;) int size
    ) {
        try {
<span class="fc" id="L51">            Object result = elasticsearchSyncService.searchItemsByKeyword(keyword, page, size);</span>
            
            // 检查是否有回退标记
<span class="pc bpc" id="L54" title="1 of 2 branches missed.">            if (result instanceof Map) {</span>
<span class="fc" id="L55">                Map&lt;String, Object&gt; resultMap = (Map&lt;String, Object&gt;) result;</span>
<span class="fc bfc" id="L56" title="All 2 branches covered.">                if (&quot;elasticsearch_fallback&quot;.equals(resultMap.get(&quot;source&quot;))) {</span>
<span class="fc" id="L57">                    logger.info(&quot;检测到ES搜索回退标记&quot;);</span>
<span class="fc" id="L58">                    return ResponseEntity.ok(ApiResponse.success(&quot;由于Elasticsearch兼容性问题，已回退到MySQL搜索&quot;, result));</span>
                }
<span class="fc bfc" id="L60" title="All 2 branches covered.">                if (resultMap.containsKey(&quot;error&quot;)) {</span>
<span class="fc" id="L61">                    logger.warn(&quot;ES搜索返回错误: {}&quot;, resultMap.get(&quot;error&quot;));</span>
<span class="fc" id="L62">                    return ResponseEntity.ok(ApiResponse.error(&quot;ES_SEARCH_ERROR&quot;, resultMap.get(&quot;error&quot;).toString(), result));</span>
                }
            }
            
<span class="fc" id="L66">            return ResponseEntity.ok(ApiResponse.success(result));</span>
<span class="fc" id="L67">        } catch (Exception e) {</span>
<span class="fc" id="L68">            logger.error(&quot;ES搜索错误: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L69">            return ResponseEntity.status(500).body(ApiResponse.error(&quot;ES_SEARCH_ERROR&quot;, &quot;搜索过程中出错: &quot; + e.getMessage()));</span>
        }
    }

    @GetMapping(&quot;/search/advanced&quot;)
    @Operation(summary = &quot;高级搜索物品&quot;, description = &quot;从ES中高级搜索物品，支持多种筛选条件和排序&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; advancedSearch(
            @RequestParam(value = &quot;q&quot;, required = false) String keyword,
            @RequestParam(value = &quot;category_id&quot;, required = false) Long categoryId,
            @RequestParam(value = &quot;price_min&quot;, required = false) Double priceMin,
            @RequestParam(value = &quot;price_max&quot;, required = false) Double priceMax,
            @RequestParam(value = &quot;item_type&quot;, required = false) String itemType,
            @RequestParam(value = &quot;condition&quot;, required = false) String condition,
            @RequestParam(value = &quot;page&quot;, defaultValue = &quot;0&quot;) int page,
            @RequestParam(value = &quot;size&quot;, defaultValue = &quot;10&quot;) int size,
            @RequestParam(value = &quot;sort&quot;, required = false) String sort
    ) {
        try {
<span class="fc" id="L87">            Object result = elasticsearchSyncService.advancedSearch(keyword, categoryId, priceMin, priceMax, </span>
                                                                   itemType, condition, page, size, sort);
            
            // 检查是否有回退标记
<span class="pc bpc" id="L91" title="1 of 2 branches missed.">            if (result instanceof Map) {</span>
<span class="fc" id="L92">                Map&lt;String, Object&gt; resultMap = (Map&lt;String, Object&gt;) result;</span>
<span class="fc bfc" id="L93" title="All 2 branches covered.">                if (&quot;elasticsearch_fallback&quot;.equals(resultMap.get(&quot;source&quot;))) {</span>
<span class="fc" id="L94">                    logger.info(&quot;检测到ES搜索回退标记&quot;);</span>
<span class="fc" id="L95">                    return ResponseEntity.ok(ApiResponse.success(&quot;由于Elasticsearch兼容性问题，已回退到MySQL搜索&quot;, result));</span>
                }
<span class="fc bfc" id="L97" title="All 2 branches covered.">                if (resultMap.containsKey(&quot;error&quot;)) {</span>
<span class="fc" id="L98">                    logger.warn(&quot;ES高级搜索返回错误: {}&quot;, resultMap.get(&quot;error&quot;));</span>
<span class="fc" id="L99">                    return ResponseEntity.ok(ApiResponse.error(&quot;ES_SEARCH_ERROR&quot;, resultMap.get(&quot;error&quot;).toString(), result));</span>
                }
            }
            
<span class="fc" id="L103">            return ResponseEntity.ok(ApiResponse.success(result));</span>
<span class="fc" id="L104">        } catch (Exception e) {</span>
<span class="fc" id="L105">            logger.error(&quot;ES高级搜索错误: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L106">            return ResponseEntity.status(500).body(ApiResponse.error(&quot;ES_ADVANCED_SEARCH_ERROR&quot;, &quot;高级搜索过程中出错: &quot; + e.getMessage()));</span>
        }
    }

    @GetMapping(&quot;/direct/search&quot;)
    @Operation(summary = &quot;直接通过ES REST API搜索&quot;, description = &quot;直接向Elasticsearch服务器发送REST请求进行搜索&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; directSearch(
            @RequestParam(&quot;q&quot;) String keyword,
            @RequestParam(value = &quot;page&quot;, defaultValue = &quot;0&quot;) int page,
            @RequestParam(value = &quot;size&quot;, defaultValue = &quot;10&quot;) int size
    ) {
        try {
<span class="fc" id="L118">            logger.info(&quot;直接ES搜索API被调用 - 关键词: {}, 页码: {}, 大小: {}&quot;, keyword, page, size);</span>
<span class="fc" id="L119">            Object result = elasticsearchSyncService.directSearchByKeyword(keyword, page, size);</span>
            
<span class="pc bpc" id="L121" title="1 of 2 branches missed.">            logger.info(&quot;直接ES搜索完成，结果类型: {}&quot;, result != null ? result.getClass().getName() : &quot;null&quot;);</span>
            
            // 检查是否有错误信息
<span class="pc bpc" id="L124" title="1 of 2 branches missed.">            if (result instanceof Map) {</span>
<span class="fc" id="L125">                Map&lt;String, Object&gt; resultMap = (Map&lt;String, Object&gt;) result;</span>
                
<span class="pc bpc" id="L127" title="1 of 2 branches missed.">                if (resultMap.containsKey(&quot;source&quot;)) {</span>
<span class="nc" id="L128">                    String source = (String) resultMap.get(&quot;source&quot;);</span>
<span class="nc" id="L129">                    logger.info(&quot;结果source标记: {}&quot;, source);</span>
                }
                
<span class="fc bfc" id="L132" title="All 2 branches covered.">                if (resultMap.containsKey(&quot;error&quot;)) {</span>
<span class="fc" id="L133">                    logger.warn(&quot;直接ES搜索返回错误: {}&quot;, resultMap.get(&quot;error&quot;));</span>
<span class="fc" id="L134">                    return ResponseEntity.ok(ApiResponse.error(&quot;ES_DIRECT_SEARCH_ERROR&quot;, resultMap.get(&quot;error&quot;).toString(), result));</span>
                }
                
                // 将结果转换为前端期望的格式
<span class="fc" id="L138">                Map&lt;String, Object&gt; formattedResponse = formatSearchResponse(resultMap);</span>
<span class="fc" id="L139">                logger.info(&quot;直接ES搜索成功返回&quot;);</span>
<span class="fc" id="L140">                return ResponseEntity.ok(ApiResponse.success(formattedResponse));</span>
            }
            
<span class="nc" id="L143">            logger.info(&quot;直接ES搜索成功返回&quot;);</span>
<span class="nc" id="L144">            return ResponseEntity.ok(ApiResponse.success(result));</span>
<span class="fc" id="L145">        } catch (Exception e) {</span>
<span class="fc" id="L146">            logger.error(&quot;直接ES搜索错误: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L147">            return ResponseEntity.status(500).body(ApiResponse.error(&quot;ES_DIRECT_SEARCH_ERROR&quot;, &quot;搜索过程中出错: &quot; + e.getMessage()));</span>
        }
    }

    @GetMapping(&quot;/direct/search/advanced&quot;)
    @Operation(summary = &quot;直接通过ES REST API高级搜索&quot;, description = &quot;直接向Elasticsearch服务器发送REST请求进行高级搜索&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; directAdvancedSearch(
            @RequestParam(value = &quot;q&quot;, required = false) String keyword,
            @RequestParam(value = &quot;category_id&quot;, required = false) Long categoryId,
            @RequestParam(value = &quot;price_min&quot;, required = false) Double priceMin,
            @RequestParam(value = &quot;price_max&quot;, required = false) Double priceMax,
            @RequestParam(value = &quot;item_type&quot;, required = false) String itemType,
            @RequestParam(value = &quot;condition&quot;, required = false) String condition,
            @RequestParam(value = &quot;page&quot;, defaultValue = &quot;0&quot;) int page,
            @RequestParam(value = &quot;size&quot;, defaultValue = &quot;10&quot;) int size,
            @RequestParam(value = &quot;sort&quot;, required = false) String sort
    ) {
        try {
<span class="fc" id="L165">            logger.info(&quot;直接ES高级搜索API被调用 - 关键词: {}, 分类: {}, 页码: {}, 大小: {}, 排序: {}&quot;, </span>
<span class="fc" id="L166">                       keyword, categoryId, page, size, sort);</span>
            
<span class="fc" id="L168">            Object result = elasticsearchSyncService.directAdvancedSearch(keyword, categoryId, priceMin, priceMax, </span>
                                                                       itemType, condition, page, size, sort);
            
<span class="pc bpc" id="L171" title="1 of 2 branches missed.">            logger.info(&quot;直接ES高级搜索完成，结果类型: {}&quot;, result != null ? result.getClass().getName() : &quot;null&quot;);</span>
            
            // 检查是否有错误信息
<span class="pc bpc" id="L174" title="1 of 2 branches missed.">            if (result instanceof Map) {</span>
<span class="fc" id="L175">                Map&lt;String, Object&gt; resultMap = (Map&lt;String, Object&gt;) result;</span>
                
<span class="pc bpc" id="L177" title="1 of 2 branches missed.">                if (resultMap.containsKey(&quot;source&quot;)) {</span>
<span class="nc" id="L178">                    String source = (String) resultMap.get(&quot;source&quot;);</span>
<span class="nc" id="L179">                    logger.info(&quot;结果source标记: {}&quot;, source);</span>
                }
                
<span class="fc bfc" id="L182" title="All 2 branches covered.">                if (resultMap.containsKey(&quot;error&quot;)) {</span>
<span class="fc" id="L183">                    logger.warn(&quot;直接ES高级搜索返回错误: {}&quot;, resultMap.get(&quot;error&quot;));</span>
<span class="fc" id="L184">                    return ResponseEntity.ok(ApiResponse.error(&quot;ES_DIRECT_ADVANCED_SEARCH_ERROR&quot;, resultMap.get(&quot;error&quot;).toString(), result));</span>
                }
                
                // 将结果转换为前端期望的格式
<span class="fc" id="L188">                Map&lt;String, Object&gt; formattedResponse = formatSearchResponse(resultMap);</span>
<span class="fc" id="L189">                logger.info(&quot;直接ES高级搜索成功返回&quot;);</span>
<span class="fc" id="L190">                return ResponseEntity.ok(ApiResponse.success(formattedResponse));</span>
            }
            
<span class="nc" id="L193">            logger.info(&quot;直接ES高级搜索成功返回&quot;);</span>
<span class="nc" id="L194">            return ResponseEntity.ok(ApiResponse.success(result));</span>
<span class="fc" id="L195">        } catch (Exception e) {</span>
<span class="fc" id="L196">            logger.error(&quot;直接ES高级搜索错误: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L197">            return ResponseEntity.status(500).body(ApiResponse.error(&quot;ES_DIRECT_ADVANCED_SEARCH_ERROR&quot;, &quot;高级搜索过程中出错: &quot; + e.getMessage()));</span>
        }
    }

    @GetMapping(&quot;/test/direct&quot;)
    @Operation(summary = &quot;测试直接ES搜索&quot;, description = &quot;简化的直接ES搜索测试端点&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; testDirectSearch(
            @RequestParam(value = &quot;q&quot;, defaultValue = &quot;手机&quot;) String keyword
    ) {
        try {
<span class="fc" id="L207">            logger.info(&quot;测试直接ES搜索API被调用 - 关键词: {}&quot;, keyword);</span>
            
            // 固定参数以简化测试
<span class="fc" id="L210">            int page = 0;</span>
<span class="fc" id="L211">            int size = 10;</span>
            
<span class="fc" id="L213">            Object result = elasticsearchSyncService.directSearchByKeyword(keyword, page, size);</span>
<span class="pc bpc" id="L214" title="1 of 2 branches missed.">            logger.info(&quot;测试直接ES搜索完成，结果类型: {}&quot;, result != null ? result.getClass().getName() : &quot;null&quot;);</span>
            
            // 检查是否有错误信息
<span class="pc bpc" id="L217" title="1 of 2 branches missed.">            if (result instanceof Map) {</span>
<span class="fc" id="L218">                Map&lt;String, Object&gt; resultMap = (Map&lt;String, Object&gt;) result;</span>
                
<span class="pc bpc" id="L220" title="1 of 2 branches missed.">                if (resultMap.containsKey(&quot;source&quot;)) {</span>
<span class="nc" id="L221">                    String source = (String) resultMap.get(&quot;source&quot;);</span>
<span class="nc" id="L222">                    logger.info(&quot;结果source标记: {}&quot;, source);</span>
                }
                
<span class="fc bfc" id="L225" title="All 2 branches covered.">                if (resultMap.containsKey(&quot;error&quot;)) {</span>
<span class="fc" id="L226">                    logger.warn(&quot;测试直接ES搜索返回错误: {}&quot;, resultMap.get(&quot;error&quot;));</span>
<span class="fc" id="L227">                    return ResponseEntity.ok(ApiResponse.error(&quot;ES_TEST_ERROR&quot;, resultMap.get(&quot;error&quot;).toString(), result));</span>
                }
                
                // 检查items是否有内容
<span class="fc" id="L231">                List&lt;?&gt; items = (List&lt;?&gt;) resultMap.get(&quot;items&quot;);</span>
<span class="pc bpc" id="L232" title="1 of 2 branches missed.">                logger.info(&quot;搜索结果物品数量: {}&quot;, items != null ? items.size() : 0);</span>
                
                // 打印每个物品的关键字段，用于调试
<span class="pc bpc" id="L235" title="1 of 2 branches missed.">                if (items != null) {</span>
<span class="pc bpc" id="L236" title="1 of 4 branches missed.">                    for (int i = 0; i &lt; items.size() &amp;&amp; i &lt; 3; i++) { // 只打印前3个结果</span>
<span class="fc" id="L237">                        Map&lt;String, Object&gt; item = (Map&lt;String, Object&gt;) items.get(i);</span>
<span class="fc" id="L238">                        logger.info(&quot;物品 #{}: id={}, name={}&quot;, i+1, item.get(&quot;id&quot;), item.get(&quot;name&quot;));</span>
                        
                        // 详细检查图片相关字段
<span class="pc bpc" id="L241" title="1 of 2 branches missed.">                        logger.info(&quot;  - imageUrls字段: {}&quot;, item.get(&quot;imageUrls&quot;) != null ? &quot;存在&quot; : &quot;不存在&quot;);</span>
<span class="pc bpc" id="L242" title="1 of 2 branches missed.">                        if (item.containsKey(&quot;imageUrls&quot;)) {</span>
<span class="nc" id="L243">                            List&lt;String&gt; urls = (List&lt;String&gt;) item.get(&quot;imageUrls&quot;);</span>
<span class="nc bnc" id="L244" title="All 2 branches missed.">                            logger.info(&quot;    - imageUrls内容: {}&quot;, urls != null ? String.join(&quot;, &quot;, urls) : &quot;null&quot;);</span>
                        }
                        
<span class="pc bpc" id="L247" title="1 of 2 branches missed.">                        logger.info(&quot;  - images字段: {}&quot;, item.get(&quot;images&quot;) != null ? &quot;存在&quot; : &quot;不存在&quot;);</span>
<span class="pc bpc" id="L248" title="1 of 2 branches missed.">                        if (item.containsKey(&quot;images&quot;)) {</span>
<span class="nc" id="L249">                            List&lt;Map&lt;String, Object&gt;&gt; images = (List&lt;Map&lt;String, Object&gt;&gt;) item.get(&quot;images&quot;);</span>
<span class="nc bnc" id="L250" title="All 4 branches missed.">                            if (images != null &amp;&amp; !images.isEmpty()) {</span>
<span class="nc" id="L251">                                logger.info(&quot;    - images数量: {}&quot;, images.size());</span>
<span class="nc" id="L252">                                logger.info(&quot;    - 第一张图URL: {}&quot;, ((Map&lt;String, Object&gt;)images.get(0)).get(&quot;url&quot;));</span>
<span class="nc" id="L253">                                logger.info(&quot;    - 第一张图isMain: {}&quot;, ((Map&lt;String, Object&gt;)images.get(0)).get(&quot;isMain&quot;));</span>
                            } else {
<span class="nc" id="L255">                                logger.info(&quot;    - images为空列表&quot;);</span>
                            }
                        }
                        
<span class="fc" id="L259">                        logger.info(&quot;  - mainImage字段: {}&quot;, item.get(&quot;mainImage&quot;));</span>
                        
                        // 检查用户信息
<span class="pc bpc" id="L262" title="1 of 2 branches missed.">                        logger.info(&quot;  - user字段: {}&quot;, item.get(&quot;user&quot;) != null ? &quot;存在&quot; : &quot;不存在&quot;);</span>
<span class="pc bpc" id="L263" title="1 of 2 branches missed.">                        if (item.containsKey(&quot;user&quot;)) {</span>
<span class="nc" id="L264">                            Map&lt;String, Object&gt; user = (Map&lt;String, Object&gt;) item.get(&quot;user&quot;);</span>
<span class="nc bnc" id="L265" title="All 2 branches missed.">                            if (user != null) {</span>
<span class="nc" id="L266">                                logger.info(&quot;    - 用户信息: id={}, name={}, 头像: {}&quot;, </span>
<span class="nc" id="L267">                                          user.get(&quot;id&quot;), user.get(&quot;username&quot;), user.get(&quot;avatarUrl&quot;));</span>
                            }
                        }
                        
<span class="pc bpc" id="L271" title="1 of 2 branches missed.">                        logger.info(&quot;  - seller字段: {}&quot;, item.get(&quot;seller&quot;) != null ? &quot;存在&quot; : &quot;不存在&quot;);</span>
                    }
                }
            }
            
<span class="fc" id="L276">            logger.info(&quot;测试直接ES搜索成功返回&quot;);</span>
<span class="fc" id="L277">            return ResponseEntity.ok(ApiResponse.success(result));</span>
<span class="fc" id="L278">        } catch (Exception e) {</span>
<span class="fc" id="L279">            logger.error(&quot;测试直接ES搜索错误: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L280">            return ResponseEntity.status(500).body(ApiResponse.error(&quot;ES_TEST_ERROR&quot;, &quot;搜索过程中出错: &quot; + e.getMessage()));</span>
        }
    }

    @GetMapping(&quot;/test/format&quot;)
    @Operation(summary = &quot;测试格式化后的ES搜索结果&quot;, description = &quot;测试格式化后的ES搜索结果是否与普通搜索一致&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; testFormattedSearch(
            @RequestParam(value = &quot;q&quot;, defaultValue = &quot;手机&quot;) String keyword
    ) {
        try {
<span class="fc" id="L290">            logger.info(&quot;测试格式化后的ES搜索API被调用 - 关键词: {}&quot;, keyword);</span>
            
            // 固定参数以简化测试
<span class="fc" id="L293">            int page = 0;</span>
<span class="fc" id="L294">            int size = 10;</span>
            
            // 获取ES搜索结果
<span class="fc" id="L297">            Object esResult = elasticsearchSyncService.directSearchByKeyword(keyword, page, size);</span>
            
            // 检查是否有错误信息
<span class="pc bpc" id="L300" title="1 of 2 branches missed.">            if (esResult instanceof Map) {</span>
<span class="fc" id="L301">                Map&lt;String, Object&gt; resultMap = (Map&lt;String, Object&gt;) esResult;</span>
                
<span class="fc bfc" id="L303" title="All 2 branches covered.">                if (resultMap.containsKey(&quot;error&quot;)) {</span>
<span class="fc" id="L304">                    logger.warn(&quot;ES搜索返回错误: {}&quot;, resultMap.get(&quot;error&quot;));</span>
<span class="fc" id="L305">                    return ResponseEntity.ok(ApiResponse.error(&quot;ES_TEST_ERROR&quot;, resultMap.get(&quot;error&quot;).toString(), resultMap));</span>
                }
                
                // 将结果转换为前端期望的格式
<span class="fc" id="L309">                Map&lt;String, Object&gt; formattedResponse = formatSearchResponse(resultMap);</span>
                
                // 检查第一个物品的关键字段
<span class="fc" id="L312">                List&lt;Map&lt;String, Object&gt;&gt; items = (List&lt;Map&lt;String, Object&gt;&gt;) formattedResponse.get(&quot;items&quot;);</span>
<span class="pc bpc" id="L313" title="2 of 4 branches missed.">                if (items != null &amp;&amp; !items.isEmpty()) {</span>
<span class="fc" id="L314">                    Map&lt;String, Object&gt; firstItem = items.get(0);</span>
<span class="fc" id="L315">                    logger.info(&quot;格式化后第一个物品: id={}, name={}, title={}&quot;, </span>
<span class="fc" id="L316">                              firstItem.get(&quot;id&quot;), firstItem.get(&quot;name&quot;), firstItem.get(&quot;title&quot;));</span>
                    
                    // 检查images字段
<span class="fc" id="L319">                    Object images = firstItem.get(&quot;images&quot;);</span>
<span class="pc bpc" id="L320" title="1 of 2 branches missed.">                    if (images instanceof List) {</span>
<span class="nc" id="L321">                        List&lt;?&gt; imagesList = (List&lt;?&gt;) images;</span>
<span class="nc bnc" id="L322" title="All 2 branches missed.">                        if (!imagesList.isEmpty()) {</span>
<span class="nc" id="L323">                            Object firstImage = imagesList.get(0);</span>
<span class="nc" id="L324">                            logger.info(&quot;images字段类型: {}, 第一个元素类型: {}&quot;, </span>
<span class="nc" id="L325">                                      images.getClass().getName(),</span>
<span class="nc bnc" id="L326" title="All 2 branches missed.">                                      firstImage != null ? firstImage.getClass().getName() : &quot;null&quot;);</span>
<span class="nc" id="L327">                            logger.info(&quot;images第一个元素: {}&quot;, firstImage);</span>
                        }
                    }
                    
                    // 检查用户字段
<span class="fc" id="L332">                    Map&lt;String, Object&gt; user = (Map&lt;String, Object&gt;) firstItem.get(&quot;user&quot;);</span>
<span class="pc bpc" id="L333" title="1 of 2 branches missed.">                    if (user != null) {</span>
<span class="nc" id="L334">                        logger.info(&quot;用户信息: id={}, username={}, avatar={}, avatarUrl={}&quot;, </span>
<span class="nc" id="L335">                                  user.get(&quot;id&quot;), user.get(&quot;username&quot;), user.get(&quot;avatar&quot;), user.get(&quot;avatarUrl&quot;));</span>
                    }
                }
                
<span class="fc" id="L339">                logger.info(&quot;格式化后的ES搜索成功返回&quot;);</span>
<span class="fc" id="L340">                return ResponseEntity.ok(ApiResponse.success(&quot;格式化后的ES搜索结果&quot;, formattedResponse));</span>
            }
            
<span class="nc" id="L343">            return ResponseEntity.ok(ApiResponse.success(&quot;ES搜索结果&quot;, esResult));</span>
<span class="fc" id="L344">        } catch (Exception e) {</span>
<span class="fc" id="L345">            logger.error(&quot;测试格式化后的ES搜索错误: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L346">            return ResponseEntity.status(500).body(ApiResponse.error(&quot;ES_TEST_ERROR&quot;, &quot;搜索过程中出错: &quot; + e.getMessage()));</span>
        }
    }

    @GetMapping(&quot;/test/format2&quot;)
    @Operation(summary = &quot;测试修改后的ES搜索结果格式&quot;, description = &quot;验证修改后的ES搜索结果格式是否与普通搜索完全一致&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; testFormattedSearch2(
            @RequestParam(value = &quot;q&quot;, defaultValue = &quot;相机&quot;) String keyword
    ) {
        try {
<span class="fc" id="L356">            logger.info(&quot;测试修改后的ES搜索API被调用 - 关键词: {}&quot;, keyword);</span>
            
            // 固定参数以简化测试
<span class="fc" id="L359">            int page = 0;</span>
<span class="fc" id="L360">            int size = 10;</span>
            
            // 获取ES搜索结果
<span class="fc" id="L363">            Object esResult = elasticsearchSyncService.directSearchByKeyword(keyword, page, size);</span>
            
            // 检查是否有错误信息
<span class="pc bpc" id="L366" title="1 of 2 branches missed.">            if (esResult instanceof Map) {</span>
<span class="fc" id="L367">                Map&lt;String, Object&gt; resultMap = (Map&lt;String, Object&gt;) esResult;</span>
                
<span class="fc bfc" id="L369" title="All 2 branches covered.">                if (resultMap.containsKey(&quot;error&quot;)) {</span>
<span class="fc" id="L370">                    logger.warn(&quot;ES搜索返回错误: {}&quot;, resultMap.get(&quot;error&quot;));</span>
<span class="fc" id="L371">                    return ResponseEntity.ok(ApiResponse.error(&quot;ES_TEST_ERROR&quot;, resultMap.get(&quot;error&quot;).toString(), resultMap));</span>
                }
                
                // 将结果转换为前端期望的格式
<span class="fc" id="L375">                Map&lt;String, Object&gt; formattedResponse = formatSearchResponse(resultMap);</span>
                
                // 检查第一个物品的关键字段
<span class="fc" id="L378">                List&lt;Map&lt;String, Object&gt;&gt; items = (List&lt;Map&lt;String, Object&gt;&gt;) formattedResponse.get(&quot;items&quot;);</span>
<span class="pc bpc" id="L379" title="2 of 4 branches missed.">                if (items != null &amp;&amp; !items.isEmpty()) {</span>
<span class="fc" id="L380">                    Map&lt;String, Object&gt; firstItem = items.get(0);</span>
<span class="fc" id="L381">                    logger.info(&quot;格式化后第一个物品: id={}, name={}, title={}&quot;, </span>
<span class="fc" id="L382">                              firstItem.get(&quot;id&quot;), firstItem.get(&quot;name&quot;), firstItem.get(&quot;title&quot;));</span>
                    
                    // 检查images字段
<span class="fc" id="L385">                    Object images = firstItem.get(&quot;images&quot;);</span>
<span class="pc bpc" id="L386" title="1 of 2 branches missed.">                    if (images instanceof List) {</span>
<span class="nc" id="L387">                        List&lt;?&gt; imagesList = (List&lt;?&gt;) images;</span>
<span class="nc bnc" id="L388" title="All 2 branches missed.">                        if (!imagesList.isEmpty()) {</span>
<span class="nc" id="L389">                            Object firstImage = imagesList.get(0);</span>
<span class="nc" id="L390">                            logger.info(&quot;images字段类型: {}, 第一个元素类型: {}&quot;, </span>
<span class="nc" id="L391">                                      images.getClass().getName(),</span>
<span class="nc bnc" id="L392" title="All 2 branches missed.">                                      firstImage != null ? firstImage.getClass().getName() : &quot;null&quot;);</span>
<span class="nc" id="L393">                            logger.info(&quot;images第一个元素: {}&quot;, firstImage);</span>
                        }
                    }
                    
                    // 检查用户字段
<span class="fc" id="L398">                    Map&lt;String, Object&gt; user = (Map&lt;String, Object&gt;) firstItem.get(&quot;user&quot;);</span>
<span class="pc bpc" id="L399" title="1 of 2 branches missed.">                    if (user != null) {</span>
<span class="nc" id="L400">                        logger.info(&quot;用户信息: id={}, username={}, avatar={}, avatarUrl={}&quot;, </span>
<span class="nc" id="L401">                                  user.get(&quot;id&quot;), user.get(&quot;username&quot;), user.get(&quot;avatar&quot;), user.get(&quot;avatarUrl&quot;));</span>
                    }
                    
                    // 检查是否有ES特有字段
<span class="fc" id="L405">                    logger.info(&quot;检查ES特有字段: _class={}, isVisible={}, nameHighlight={}, score={}, mainImage={}&quot;, </span>
<span class="fc" id="L406">                              firstItem.containsKey(&quot;_class&quot;),</span>
<span class="fc" id="L407">                              firstItem.containsKey(&quot;isVisible&quot;),</span>
<span class="fc" id="L408">                              firstItem.containsKey(&quot;nameHighlight&quot;),</span>
<span class="fc" id="L409">                              firstItem.containsKey(&quot;score&quot;),</span>
<span class="fc" id="L410">                              firstItem.containsKey(&quot;mainImage&quot;));</span>
                    
                    // 检查所有字段
<span class="fc" id="L413">                    logger.info(&quot;物品所有字段: {}&quot;, firstItem.keySet());</span>
                }
                
<span class="fc" id="L416">                logger.info(&quot;格式化后的ES搜索成功返回&quot;);</span>
<span class="fc" id="L417">                return ResponseEntity.ok(ApiResponse.success(&quot;格式化后的ES搜索结果&quot;, formattedResponse));</span>
            }
            
<span class="nc" id="L420">            return ResponseEntity.ok(ApiResponse.success(&quot;ES搜索结果&quot;, esResult));</span>
<span class="fc" id="L421">        } catch (Exception e) {</span>
<span class="fc" id="L422">            logger.error(&quot;测试格式化后的ES搜索错误: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L423">            return ResponseEntity.status(500).body(ApiResponse.error(&quot;ES_TEST_ERROR&quot;, &quot;搜索过程中出错: &quot; + e.getMessage()));</span>
        }
    }

    /**
     * 将ES搜索结果格式化为前端期望的格式
     */
    @SuppressWarnings(&quot;unchecked&quot;)
    private Map&lt;String, Object&gt; formatSearchResponse(Map&lt;String, Object&gt; esResult) {
<span class="fc" id="L432">        Map&lt;String, Object&gt; formattedResponse = new HashMap&lt;&gt;();</span>
        
        // 提取items
<span class="fc" id="L435">        List&lt;Map&lt;String, Object&gt;&gt; items = (List&lt;Map&lt;String, Object&gt;&gt;) esResult.get(&quot;items&quot;);</span>
<span class="pc bpc" id="L436" title="1 of 2 branches missed.">        formattedResponse.put(&quot;items&quot;, items != null ? items : new ArrayList&lt;&gt;());</span>
        
        // 构建分页信息
<span class="fc" id="L439">        int total = ((Number) esResult.getOrDefault(&quot;total&quot;, 0)).intValue();</span>
<span class="fc" id="L440">        int page = ((Number) esResult.getOrDefault(&quot;page&quot;, 0)).intValue();</span>
<span class="fc" id="L441">        int size = ((Number) esResult.getOrDefault(&quot;size&quot;, 10)).intValue();</span>
<span class="pc bpc" id="L442" title="1 of 2 branches missed.">        int totalPages = size &gt; 0 ? (int) Math.ceil((double) total / size) : 0;</span>
        
        // 与普通搜索保持一致的字段名
<span class="fc" id="L445">        formattedResponse.put(&quot;currentPage&quot;, page);</span>
<span class="fc" id="L446">        formattedResponse.put(&quot;size&quot;, size);</span>
<span class="fc" id="L447">        formattedResponse.put(&quot;totalItems&quot;, total);</span>
<span class="fc" id="L448">        formattedResponse.put(&quot;totalPages&quot;, totalPages);</span>
<span class="fc" id="L449">        formattedResponse.put(&quot;content&quot;, items); // 普通搜索有content字段</span>
<span class="fc" id="L450">        formattedResponse.put(&quot;page&quot;, page); // 普通搜索同时有page和currentPage</span>
<span class="fc" id="L451">        formattedResponse.put(&quot;totalElements&quot;, total); // 普通搜索同时有totalItems和totalElements</span>
        
        // 普通搜索中source为null
<span class="fc" id="L454">        formattedResponse.put(&quot;source&quot;, null);</span>
        
        // 移除ES特有的字段
        // formattedResponse.put(&quot;took&quot;, esResult.getOrDefault(&quot;took&quot;, null));
        
<span class="fc" id="L459">        return formattedResponse;</span>
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>