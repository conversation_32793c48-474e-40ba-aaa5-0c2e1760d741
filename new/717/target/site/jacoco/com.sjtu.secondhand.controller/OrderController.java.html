<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OrderController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">OrderController.java</span></div><h1>OrderController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.OrderRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.OrderResponse;
import com.sjtu.secondhand.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping(&quot;/orders&quot;)
@Tag(name = &quot;订单 (Orders)&quot;, description = &quot;订单相关接口&quot;)
public class OrderController {

    private final OrderService orderService;

<span class="fc" id="L28">    public OrderController(OrderService orderService) {</span>
<span class="fc" id="L29">        this.orderService = orderService;</span>
<span class="fc" id="L30">    }</span>

    // 创建订单
    @PostMapping
    @Operation(summary = &quot;创建订单 (预订闲置物品)&quot;, description = &quot;买家对一个 'IDLE' 类型的物品发起预订，创建一个订单，物品状态变为 'RESERVED'&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;OrderResponse&gt;&gt; createOrder(@Valid @RequestBody OrderRequest orderRequest) {
<span class="fc" id="L37">        OrderResponse orderResponse = orderService.createOrder(orderRequest);</span>
<span class="fc" id="L38">        return new ResponseEntity&lt;&gt;(ApiResponse.success(&quot;订单创建成功&quot;, orderResponse), HttpStatus.CREATED);</span>
    }

    // 获取订单详情
    @GetMapping(&quot;/{id}&quot;)
    @Operation(summary = &quot;获取订单详情&quot;, description = &quot;获取指定ID的订单详细信息&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;OrderResponse&gt;&gt; getOrderById(@PathVariable Long id) {
<span class="fc" id="L46">        OrderResponse orderResponse = orderService.getOrderById(id);</span>
<span class="fc" id="L47">        return ResponseEntity.ok(ApiResponse.success(&quot;获取订单成功&quot;, orderResponse));</span>
    }

    // 获取我购买的订单列表
    @GetMapping(&quot;/my/bought&quot;)
    @Operation(summary = &quot;获取我购买的订单列表&quot;, description = &quot;获取当前用户作为买家的订单列表&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Page&lt;OrderResponse&gt;&gt;&gt; getMyBoughtOrders(
            @RequestParam(defaultValue = &quot;1&quot;) int page,
            @RequestParam(defaultValue = &quot;20&quot;) int size) {
        // 调整页码，因为API文档从1开始，而Spring Data从0开始
<span class="fc bfc" id="L58" title="All 2 branches covered.">        page = page &gt; 0 ? page - 1 : 0;</span>
<span class="fc" id="L59">        Pageable pageable = PageRequest.of(page, size, Sort.by(&quot;createdAt&quot;).descending());</span>
<span class="fc" id="L60">        Page&lt;OrderResponse&gt; orders = orderService.getMyBoughtOrders(pageable);</span>
<span class="fc" id="L61">        return ResponseEntity.ok(ApiResponse.success(&quot;获取购买订单列表成功&quot;, orders));</span>
    }

    // 获取我出售的订单列表
    @GetMapping(&quot;/my/sold&quot;)
    @Operation(summary = &quot;获取我出售的订单列表&quot;, description = &quot;获取当前用户作为卖家的订单列表&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Page&lt;OrderResponse&gt;&gt;&gt; getMySoldOrders(
            @RequestParam(defaultValue = &quot;1&quot;) int page,
            @RequestParam(defaultValue = &quot;20&quot;) int size) {
        // 调整页码，因为API文档从1开始，而Spring Data从0开始
<span class="fc bfc" id="L72" title="All 2 branches covered.">        page = page &gt; 0 ? page - 1 : 0;</span>
<span class="fc" id="L73">        Pageable pageable = PageRequest.of(page, size, Sort.by(&quot;createdAt&quot;).descending());</span>
<span class="fc" id="L74">        Page&lt;OrderResponse&gt; orders = orderService.getMySoldOrders(pageable);</span>
<span class="fc" id="L75">        return ResponseEntity.ok(ApiResponse.success(&quot;获取出售订单列表成功&quot;, orders));</span>
    }

    // 确认订单（卖家操作）
    @PostMapping(&quot;/{id}/confirm&quot;)
    @Operation(summary = &quot;[卖家] 确认订单&quot;, description = &quot;卖家操作。将 'PENDING_CONFIRMATION' 状态的订单流转至 'AWAITING_ACKNOWLEDGEMENT'&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;OrderResponse&gt;&gt; confirmOrder(@PathVariable Long id) {
<span class="fc" id="L83">        OrderResponse orderResponse = orderService.confirmOrder(id);</span>
<span class="fc" id="L84">        return ResponseEntity.ok(ApiResponse.success(&quot;订单确认成功&quot;, orderResponse));</span>
    }

    // 取消订单
    @PostMapping(&quot;/{id}/cancel&quot;)
    @Operation(summary = &quot;[买/卖家] 取消订单&quot;, description = &quot;在交易完成前，由买家或卖家取消订单，状态流转至 'CANCELLED'，物品状态恢复 'FOR_SALE'&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;OrderResponse&gt;&gt; cancelOrder(@PathVariable Long id) {
<span class="fc" id="L92">        OrderResponse orderResponse = orderService.cancelOrder(id);</span>
<span class="fc" id="L93">        return ResponseEntity.ok(ApiResponse.success(&quot;订单取消成功&quot;, orderResponse));</span>
    }

    // 买家确认联系
    @PostMapping(&quot;/{id}/acknowledge&quot;)
    @Operation(summary = &quot;[买家] 确认收到联系方式&quot;, description = &quot;买家操作。确认已收到卖家联系方式，状态流转至 'CONFIRMED'&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;OrderResponse&gt;&gt; acknowledgeOrder(@PathVariable Long id) {
<span class="fc" id="L101">        OrderResponse orderResponse = orderService.confirmContact(id);</span>
<span class="fc" id="L102">        return ResponseEntity.ok(ApiResponse.success(&quot;确认联系成功&quot;, orderResponse));</span>
    }

    // 获取特定状态的我购买的订单
    @GetMapping(&quot;/my/bought/status/{status}&quot;)
    @Operation(summary = &quot;获取特定状态的我购买的订单&quot;, description = &quot;获取当前用户作为买家的特定状态订单列表&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Page&lt;OrderResponse&gt;&gt;&gt; getMyBoughtOrdersByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = &quot;1&quot;) int page,
            @RequestParam(defaultValue = &quot;20&quot;) int size) {
        // 调整页码，因为API文档从1开始，而Spring Data从0开始
<span class="nc bnc" id="L114" title="All 2 branches missed.">        page = page &gt; 0 ? page - 1 : 0;</span>
<span class="nc" id="L115">        Pageable pageable = PageRequest.of(page, size, Sort.by(&quot;createdAt&quot;).descending());</span>
<span class="nc" id="L116">        Page&lt;OrderResponse&gt; orders = orderService.getMyBoughtOrdersByStatus(status, pageable);</span>
<span class="nc" id="L117">        return ResponseEntity.ok(ApiResponse.success(&quot;获取指定状态的购买订单成功&quot;, orders));</span>
    }

    // 获取特定状态的我出售的订单
    @GetMapping(&quot;/my/sold/status/{status}&quot;)
    @Operation(summary = &quot;获取特定状态的我出售的订单&quot;, description = &quot;获取当前用户作为卖家的特定状态订单列表&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Page&lt;OrderResponse&gt;&gt;&gt; getMySoldOrdersByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = &quot;1&quot;) int page,
            @RequestParam(defaultValue = &quot;20&quot;) int size) {
        // 调整页码，因为API文档从1开始，而Spring Data从0开始
<span class="nc bnc" id="L129" title="All 2 branches missed.">        page = page &gt; 0 ? page - 1 : 0;</span>
<span class="nc" id="L130">        Pageable pageable = PageRequest.of(page, size, Sort.by(&quot;createdAt&quot;).descending());</span>
<span class="nc" id="L131">        Page&lt;OrderResponse&gt; orders = orderService.getMySoldOrdersByStatus(status, pageable);</span>
<span class="nc" id="L132">        return ResponseEntity.ok(ApiResponse.success(&quot;获取指定状态的出售订单成功&quot;, orders));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>