<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RecommendationServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">RecommendationServiceImpl.java</span></div><h1>RecommendationServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Item.ItemStatus;
import com.sjtu.secondhand.model.ItemSimilarity;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.ItemSimilarityRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.RecommendationService;
import com.sjtu.secondhand.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 推荐系统服务实现
 */
@Service
public class RecommendationServiceImpl implements RecommendationService {

<span class="fc" id="L39">    private static final Logger logger = LoggerFactory.getLogger(RecommendationServiceImpl.class);</span>

    // 热度分计算权重
    private static final double W1 = 5.0;  // 收藏权重
    private static final double W2 = 1.0;  // 浏览权重
    private static final double W3 = 0.1;  // 用户信用分权重
    private static final double W4 = 0.1;  // 用户积分权重
    
    // 推荐参数
    private static final int MAX_USER_FAVORITE_ITEMS = 5;  // 用户最多收藏物品数量
    private static final int SIMILAR_ITEMS_PER_ITEM = 4;   // 每个物品的相似物品数量
    private static final int MAX_RECOMMENDATIONS = 16;     // 最大推荐数量
    
    // 物品相似度计算参数
    private static final int MIN_INTERACTIONS = 3;  // 最小交互数
    private static final double SIMILARITY_THRESHOLD = 0.01;  // 相似度阈值
    
    // 内容推荐价格范围参数
    private static final double PRICE_RANGE_FACTOR = 0.3;  // ±30%价格范围
    
    private final ItemRepository itemRepository;
    private final ItemSimilarityRepository itemSimilarityRepository;
    private final UserRepository userRepository;
    private final UserService userService;
    private final ItemService itemService;
    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public RecommendationServiceImpl(
            ItemRepository itemRepository,
            ItemSimilarityRepository itemSimilarityRepository,
            UserRepository userRepository,
            UserService userService,
            ItemService itemService,
<span class="fc" id="L73">            JdbcTemplate jdbcTemplate) {</span>
<span class="fc" id="L74">        this.itemRepository = itemRepository;</span>
<span class="fc" id="L75">        this.itemSimilarityRepository = itemSimilarityRepository;</span>
<span class="fc" id="L76">        this.userRepository = userRepository;</span>
<span class="fc" id="L77">        this.userService = userService;</span>
<span class="fc" id="L78">        this.itemService = itemService;</span>
<span class="fc" id="L79">        this.jdbcTemplate = jdbcTemplate;</span>
<span class="fc" id="L80">    }</span>

    @Override
    @Cacheable(value = &quot;hotRecommendations&quot;, key = &quot;#limit&quot;)
    public List&lt;ItemResponse&gt; getHotRecommendations(int limit) {
<span class="fc" id="L85">        logger.info(&quot;获取热门推荐，限制数量: {}&quot;, limit);</span>

        // 使用SQL计算热度分并获取Top-N物品
<span class="fc" id="L88">        String sql = &quot;SELECT i.id &quot; +</span>
                &quot;FROM items i JOIN users u ON i.user_id = u.id &quot; +
                &quot;WHERE i.status = 'FOR_SALE' AND i.is_visible = TRUE &quot; +
                &quot;ORDER BY (&quot; + W1 + &quot; * i.favorite_count + &quot; + W2 + &quot; * i.view_count + &quot; 
                + W3 + &quot; * u.credit_score + &quot; + W4 + &quot; * u.points) DESC &quot; +
                &quot;LIMIT ?&quot;;

<span class="fc" id="L95">        List&lt;Long&gt; hotItemIds = jdbcTemplate.queryForList(sql, Long.class, limit);</span>

        // 获取物品详情
<span class="fc" id="L98">        List&lt;Item&gt; hotItems = itemRepository.findAllById(hotItemIds);</span>

        // 排序以确保顺序与查询结果匹配
<span class="fc" id="L101">        Map&lt;Long, Item&gt; itemMap = hotItems.stream().collect(Collectors.toMap(Item::getId, item -&gt; item));</span>
<span class="fc" id="L102">        List&lt;Item&gt; sortedItems = hotItemIds.stream()</span>
<span class="fc" id="L103">                .map(itemMap::get)</span>
<span class="fc" id="L104">                .filter(Objects::nonNull)</span>
<span class="fc" id="L105">                .collect(Collectors.toList());</span>

        // 转换为ItemResponse并返回
<span class="fc" id="L108">        return itemService.convertItemsWithFavoriteStatus(sortedItems);</span>
    }

    @Override
    @Cacheable(value = &quot;itemCFRecommendations&quot;, key = &quot;#userId&quot;)
    public List&lt;ItemResponse&gt; getItemCFRecommendations(Long userId, int limit) {
<span class="fc" id="L114">        logger.info(&quot;获取用户{}的个性化推荐，限制数量: {}&quot;, userId, limit);</span>

        // 设置默认推荐数量（如果未提供）
<span class="fc bfc" id="L117" title="All 2 branches covered.">        if (limit &lt;= 0) {</span>
<span class="fc" id="L118">            limit = MAX_RECOMMENDATIONS;</span>
        }

        // 获取用户
<span class="fc" id="L122">        User user = userService.getUserById(userId);</span>
<span class="pc bpc" id="L123" title="1 of 2 branches missed.">        if (user == null) {</span>
<span class="nc" id="L124">            throw new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;);</span>
        }

        // 获取用户最近的收藏（最多5个）
<span class="fc" id="L128">        Set&lt;Item&gt; userItems = user.getFavoriteItems();</span>
<span class="fc bfc" id="L129" title="All 2 branches covered.">        if (userItems.isEmpty()) {</span>
            // 冷启动：如果用户没有收藏，返回热门推荐
<span class="fc" id="L131">            logger.info(&quot;用户{}没有收藏项，返回热门推荐&quot;, userId);</span>
<span class="fc" id="L132">            return getHotRecommendations(limit);</span>
        }

        // 获取用户收藏的物品ID（最多5个，按最新收藏排序）
<span class="fc" id="L136">        List&lt;Long&gt; userItemIds = userItems.stream()</span>
<span class="fc" id="L137">                .sorted(Comparator.comparing(Item::getUpdatedAt).reversed())</span>
<span class="fc" id="L138">                .map(Item::getId)</span>
<span class="fc" id="L139">                .limit(MAX_USER_FAVORITE_ITEMS)</span>
<span class="fc" id="L140">                .collect(Collectors.toList());</span>

<span class="fc" id="L142">        logger.info(&quot;用户{}的收藏物品IDs: {}&quot;, userId, userItemIds);</span>

        // 对每个收藏物品，获取4个最相似的物品
<span class="fc" id="L145">        Map&lt;Long, Double&gt; recommendScores = new HashMap&lt;&gt;();</span>
<span class="fc bfc" id="L146" title="All 2 branches covered.">        for (Long itemId : userItemIds) {</span>
<span class="fc" id="L147">            List&lt;ItemSimilarity&gt; similarities = itemSimilarityRepository</span>
<span class="fc" id="L148">                    .findMostSimilarItems(itemId, SIMILAR_ITEMS_PER_ITEM);</span>
            
<span class="fc" id="L150">            logger.info(&quot;物品{}的相似物品数量: {}&quot;, itemId, similarities.size());</span>
            
            // 累加每个候选物品的相似度分数
<span class="fc bfc" id="L153" title="All 2 branches covered.">            for (ItemSimilarity similarity : similarities) {</span>
<span class="fc" id="L154">                Long recommendItemId = similarity.getItemId2();</span>
                // 跳过用户已收藏的物品
<span class="pc bpc" id="L156" title="1 of 2 branches missed.">                if (userItemIds.contains(recommendItemId)) {</span>
<span class="nc" id="L157">                    continue;</span>
                }
                // 加总相似度分数
<span class="fc" id="L160">                recommendScores.put(recommendItemId,</span>
<span class="fc" id="L161">                        recommendScores.getOrDefault(recommendItemId, 0.0) + similarity.getScore());</span>
<span class="fc" id="L162">            }</span>
<span class="fc" id="L163">        }</span>

<span class="fc" id="L165">        logger.info(&quot;候选推荐物品数量: {}&quot;, recommendScores.size());</span>

        // 按分数降序排序，获取Top-N
<span class="fc" id="L168">        List&lt;Long&gt; recommendItemIds = recommendScores.entrySet().stream()</span>
<span class="fc" id="L169">                .sorted(Map.Entry.&lt;Long, Double&gt;comparingByValue().reversed())</span>
<span class="fc" id="L170">                .limit(limit)</span>
<span class="fc" id="L171">                .map(Map.Entry::getKey)</span>
<span class="fc" id="L172">                .collect(Collectors.toList());</span>

        // 如果推荐数量不足，用热门物品填充
<span class="pc bpc" id="L175" title="1 of 2 branches missed.">        if (recommendItemIds.size() &lt; limit) {</span>
<span class="fc" id="L176">            int remainingCount = limit - recommendItemIds.size();</span>
<span class="fc" id="L177">            logger.info(&quot;推荐数量不足，需要填充{}个热门物品&quot;, remainingCount);</span>
            
<span class="nc" id="L179">            List&lt;ItemResponse&gt; hotItems = getHotRecommendations(limit * 2);</span>

            // 过滤掉已推荐的物品和用户的收藏
<span class="nc" id="L182">            Set&lt;Long&gt; existingIds = new HashSet&lt;&gt;(recommendItemIds);</span>
<span class="nc" id="L183">            existingIds.addAll(userItemIds);</span>

<span class="nc" id="L185">            List&lt;Long&gt; additionalItemIds = hotItems.stream()</span>
<span class="nc" id="L186">                    .map(item -&gt; Long.parseLong(item.getId().toString()))</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">                    .filter(id -&gt; !existingIds.contains(id))</span>
<span class="nc" id="L188">                    .limit(remainingCount)</span>
<span class="nc" id="L189">                    .collect(Collectors.toList());</span>

<span class="nc" id="L191">            recommendItemIds.addAll(additionalItemIds);</span>
        }

        // 获取物品详情
<span class="nc" id="L195">        List&lt;Item&gt; recommendedItems = itemRepository.findAllById(recommendItemIds);</span>
<span class="nc" id="L196">        logger.info(&quot;最终获取到的推荐物品数量: {}&quot;, recommendedItems.size());</span>

        // 排序以匹配推荐分数顺序
<span class="nc" id="L199">        Map&lt;Long, Item&gt; itemMap = recommendedItems.stream()</span>
<span class="nc" id="L200">                .collect(Collectors.toMap(Item::getId, item -&gt; item, (a, b) -&gt; a));</span>
<span class="nc" id="L201">        List&lt;Item&gt; sortedItems = recommendItemIds.stream()</span>
<span class="nc" id="L202">                .map(itemMap::get)</span>
<span class="nc" id="L203">                .filter(Objects::nonNull)</span>
<span class="nc" id="L204">                .collect(Collectors.toList());</span>

        // 转换为ItemResponse并返回
<span class="nc" id="L207">        return itemService.convertItemsWithFavoriteStatus(sortedItems);</span>
    }

    @Override
    @Cacheable(value = &quot;contentBasedRecommendations&quot;, key = &quot;#itemId + '-' + #limit&quot;)
    public List&lt;ItemResponse&gt; getContentBasedRecommendations(Long itemId, int limit) {
        // 对于商品详情页的&quot;猜你喜欢&quot;模块，我们直接从item_similarity表中获取相似商品
<span class="fc" id="L214">        logger.info(&quot;获取物品{}的相似推荐，限制数量: {}&quot;, itemId, limit);</span>

        // 默认显示4个相似商品
<span class="fc bfc" id="L217" title="All 2 branches covered.">        if (limit &lt;= 0) {</span>
<span class="fc" id="L218">            limit = SIMILAR_ITEMS_PER_ITEM;</span>
        }

        // 获取当前物品
<span class="fc" id="L222">        Item currentItem = itemRepository.findById(itemId)</span>
<span class="fc" id="L223">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;物品不存在&quot;));</span>

        // 直接从item_similarity表获取相似度最高的物品
<span class="fc" id="L226">        List&lt;ItemSimilarity&gt; similarities = itemSimilarityRepository.findMostSimilarItems(itemId, limit);</span>
        
        // 如果相似度表中没有数据，则退化为基于内容的推荐
<span class="fc bfc" id="L229" title="All 2 branches covered.">        if (similarities.isEmpty()) {</span>
<span class="fc" id="L230">            logger.info(&quot;物品{}在相似度表中没有数据，使用基于内容的推荐&quot;, itemId);</span>
<span class="fc" id="L231">            return getContentBasedRecommendationsFallback(itemId, limit);</span>
        }
        
        // 获取相似物品的IDs
<span class="fc" id="L235">        List&lt;Long&gt; similarItemIds = similarities.stream()</span>
<span class="fc" id="L236">                .map(ItemSimilarity::getItemId2)</span>
<span class="fc" id="L237">                .collect(Collectors.toList());</span>
        
        // 获取物品详情
<span class="fc" id="L240">        List&lt;Item&gt; similarItems = itemRepository.findAllById(similarItemIds);</span>
        
        // 过滤掉非在售状态的物品
<span class="fc" id="L243">        List&lt;Item&gt; availableSimilarItems = similarItems.stream()</span>
<span class="pc bpc" id="L244" title="3 of 4 branches missed.">                .filter(item -&gt; item.getStatus() == ItemStatus.FOR_SALE &amp;&amp; item.getIsVisible())</span>
<span class="fc" id="L245">                .collect(Collectors.toList());</span>
        
        // 如果有效的推荐不足，使用基于内容的推荐补充
<span class="pc bpc" id="L248" title="1 of 2 branches missed.">        if (availableSimilarItems.size() &lt; limit) {</span>
<span class="fc" id="L249">            int remainingCount = limit - availableSimilarItems.size();</span>
<span class="fc" id="L250">            logger.info(&quot;有效的相似物品不足，补充{}个基于内容的推荐&quot;, remainingCount);</span>
            
            // 保存已有的推荐物品ID
<span class="fc" id="L253">            Set&lt;Long&gt; existingIds = availableSimilarItems.stream()</span>
<span class="fc" id="L254">                    .map(Item::getId)</span>
<span class="fc" id="L255">                    .collect(Collectors.toSet());</span>
<span class="fc" id="L256">            existingIds.add(itemId); // 排除当前物品</span>
            
            // 获取额外的基于内容的推荐
<span class="fc" id="L259">            List&lt;ItemResponse&gt; additionalItems = getContentBasedRecommendationsFallback(itemId, limit * 2);</span>
<span class="fc" id="L260">            List&lt;Long&gt; additionalItemIds = additionalItems.stream()</span>
<span class="fc" id="L261">                    .map(item -&gt; Long.parseLong(item.getId().toString()))</span>
<span class="pc bpc" id="L262" title="1 of 2 branches missed.">                    .filter(id -&gt; !existingIds.contains(id))</span>
<span class="fc" id="L263">                    .limit(remainingCount)</span>
<span class="fc" id="L264">                    .collect(Collectors.toList());</span>
            
            // 获取额外推荐的物品详情
<span class="pc bpc" id="L267" title="1 of 2 branches missed.">            if (!additionalItemIds.isEmpty()) {</span>
<span class="nc" id="L268">                List&lt;Item&gt; additionalSimilarItems = itemRepository.findAllById(additionalItemIds);</span>
<span class="nc" id="L269">                availableSimilarItems.addAll(additionalSimilarItems);</span>
            }
        }
        
        // 按相似度排序
<span class="fc" id="L274">        Map&lt;Long, Double&gt; scoreMap = similarities.stream()</span>
<span class="fc" id="L275">                .collect(Collectors.toMap(ItemSimilarity::getItemId2, ItemSimilarity::getScore));</span>
        
<span class="fc" id="L277">        availableSimilarItems.sort((a, b) -&gt; </span>
<span class="nc" id="L278">            Double.compare(scoreMap.getOrDefault(b.getId(), 0.0), scoreMap.getOrDefault(a.getId(), 0.0)));</span>
        
        // 转换为ItemResponse并返回
<span class="fc" id="L281">        return itemService.convertItemsWithFavoriteStatus(availableSimilarItems);</span>
    }
    
    /**
     * 基于内容的推荐（相似物品）备用方法
     * 当相似度表中没有数据时使用
     */
    private List&lt;ItemResponse&gt; getContentBasedRecommendationsFallback(Long itemId, int limit) {
<span class="fc" id="L289">        logger.info(&quot;使用基于内容的推荐作为备用方案，物品ID: {}, 限制数量: {}&quot;, itemId, limit);</span>

        // 获取当前物品
<span class="fc" id="L292">        Item currentItem = itemRepository.findById(itemId)</span>
<span class="pc" id="L293">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;物品不存在&quot;));</span>

        // 获取分类和价格信息
<span class="fc" id="L296">        Integer categoryId = currentItem.getCategory().getId();</span>
<span class="fc" id="L297">        BigDecimal currentPrice = currentItem.getPrice();</span>

        // 计算价格范围（±30%）
<span class="fc" id="L300">        BigDecimal minPrice = currentPrice.multiply(new BigDecimal(1 - PRICE_RANGE_FACTOR));</span>
<span class="fc" id="L301">        BigDecimal maxPrice = currentPrice.multiply(new BigDecimal(1 + PRICE_RANGE_FACTOR));</span>

        // 查询同类别、价格范围内的相似物品
<span class="fc" id="L304">        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, &quot;createdAt&quot;));</span>
<span class="fc" id="L305">        List&lt;Item&gt; similarItems = itemRepository.findByCategoryIdAndStatusAndIsVisibleAndIdNotAndPriceBetween(</span>
                categoryId,
                ItemStatus.FOR_SALE,
<span class="fc" id="L308">                true,</span>
                itemId,
                minPrice,
                maxPrice,
                pageable);

        // 转换为ItemResponse并返回
<span class="fc" id="L315">        return itemService.convertItemsWithFavoriteStatus(similarItems);</span>
    }

    @Override
    @Scheduled(cron = &quot;0 0 4 * * ?&quot;) // 每天凌晨4点执行
    @CacheEvict(value = {&quot;itemCFRecommendations&quot;}, allEntries = true) // 清除缓存
    @Transactional
    public void calculateItemSimilarities() {
        // 这个方法已经被简化，因为我们假设item_similarity表中已经包含了模拟数据
<span class="fc" id="L324">        logger.info(&quot;跳过物品相似度计算，使用预先准备的数据&quot;);</span>
<span class="fc" id="L325">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>