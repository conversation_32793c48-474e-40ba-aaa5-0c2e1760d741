<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NotificationEventServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">NotificationEventServiceImpl.java</span></div><h1>NotificationEventServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.service.NotificationEventService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通知事件服务实现类
 * 基于Server-Sent Events (SSE)实现实时通知
 */
@Service
public class NotificationEventServiceImpl implements NotificationEventService {

<span class="fc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(NotificationEventServiceImpl.class);</span>

    private final ObjectMapper objectMapper;
    private final ApplicationEventPublisher eventPublisher;

    // 存储用户ID与SSE发射器的映射关系
<span class="fc" id="L33">    private final Map&lt;Long, SseEmitter&gt; userEmitters = new ConcurrentHashMap&lt;&gt;();</span>

    @Autowired
<span class="fc" id="L36">    public NotificationEventServiceImpl(ObjectMapper objectMapper, ApplicationEventPublisher eventPublisher) {</span>
<span class="fc" id="L37">        this.objectMapper = objectMapper;</span>
<span class="fc" id="L38">        this.eventPublisher = eventPublisher;</span>
<span class="fc" id="L39">    }</span>

    /**
     * 注册用户的SSE发射器
     * 
     * @param userId  用户ID
     * @param emitter SSE发射器
     */
    public void addEmitter(Long userId, SseEmitter emitter) {
<span class="fc" id="L48">        userEmitters.put(userId, emitter);</span>

        // 设置超时回调
<span class="fc" id="L51">        emitter.onTimeout(() -&gt; {</span>
<span class="nc" id="L52">            logger.info(&quot;SSE connection timeout for user {}&quot;, userId);</span>
<span class="nc" id="L53">            userEmitters.remove(userId);</span>
<span class="nc" id="L54">        });</span>

        // 设置完成回调
<span class="fc" id="L57">        emitter.onCompletion(() -&gt; {</span>
<span class="nc" id="L58">            logger.info(&quot;SSE connection completed for user {}&quot;, userId);</span>
<span class="nc" id="L59">            userEmitters.remove(userId);</span>
<span class="nc" id="L60">        });</span>

        // 设置错误回调
<span class="fc" id="L63">        emitter.onError((ex) -&gt; {</span>
<span class="nc" id="L64">            logger.error(&quot;SSE error for user {}: {}&quot;, userId, ex.getMessage());</span>
<span class="nc" id="L65">            userEmitters.remove(userId);</span>
<span class="nc" id="L66">        });</span>
<span class="fc" id="L67">    }</span>

    /**
     * 移除用户的SSE发射器
     * 
     * @param userId 用户ID
     */
    public void removeEmitter(Long userId) {
<span class="fc" id="L75">        userEmitters.remove(userId);</span>
<span class="fc" id="L76">    }</span>

    @Override
    public void sendOrderUpdateEvent(Order order, String eventType, String message) {
        try {
            // 向买家发送通知
<span class="fc" id="L82">            sendToUser(order.getBuyer().getId(), &quot;order&quot;, Map.of(</span>
<span class="fc" id="L83">                    &quot;orderId&quot;, order.getId(),</span>
                    &quot;eventType&quot;, eventType,
                    &quot;message&quot;, message,
<span class="fc" id="L86">                    &quot;timestamp&quot;, System.currentTimeMillis()));</span>

            // 向卖家发送通知
<span class="fc" id="L89">            sendToUser(order.getSeller().getId(), &quot;order&quot;, Map.of(</span>
<span class="fc" id="L90">                    &quot;orderId&quot;, order.getId(),</span>
                    &quot;eventType&quot;, eventType,
                    &quot;message&quot;, message,
<span class="fc" id="L93">                    &quot;timestamp&quot;, System.currentTimeMillis()));</span>
<span class="nc" id="L94">        } catch (Exception e) {</span>
<span class="nc" id="L95">            logger.error(&quot;Failed to send order update event: {}&quot;, e.getMessage());</span>
<span class="fc" id="L96">        }</span>
<span class="fc" id="L97">    }</span>

    @Override
    public void sendNotificationEvent(Notification notification) {
        try {
            // 向接收者发送通知
<span class="fc" id="L103">            sendToUser(notification.getRecipient().getId(), &quot;notification&quot;, Map.of(</span>
<span class="fc" id="L104">                    &quot;id&quot;, notification.getId(),</span>
<span class="fc" id="L105">                    &quot;type&quot;, notification.getType().toString(),</span>
<span class="fc" id="L106">                    &quot;content&quot;, notification.getContent(),</span>
<span class="fc" id="L107">                    &quot;timestamp&quot;, notification.getCreatedAt()));</span>
<span class="nc" id="L108">        } catch (Exception e) {</span>
<span class="nc" id="L109">            logger.error(&quot;Failed to send notification event: {}&quot;, e.getMessage());</span>
<span class="fc" id="L110">        }</span>
<span class="fc" id="L111">    }</span>

    /**
     * 向指定用户发送事件数据
     * 
     * @param userId    用户ID
     * @param eventName 事件名称
     * @param data      事件数据
     */
    private void sendToUser(Long userId, String eventName, Object data) {
<span class="fc" id="L121">        SseEmitter emitter = userEmitters.get(userId);</span>
<span class="fc bfc" id="L122" title="All 2 branches covered.">        if (emitter != null) {</span>
            try {
<span class="fc" id="L124">                emitter.send(SseEmitter.event()</span>
<span class="fc" id="L125">                        .name(eventName)</span>
<span class="fc" id="L126">                        .data(data, MediaType.APPLICATION_JSON));</span>
<span class="nc" id="L127">            } catch (IOException e) {</span>
<span class="nc" id="L128">                logger.error(&quot;Failed to send event to user {}: {}&quot;, userId, e.getMessage());</span>
<span class="nc" id="L129">                userEmitters.remove(userId);</span>
<span class="fc" id="L130">            }</span>
        }
<span class="fc" id="L132">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>