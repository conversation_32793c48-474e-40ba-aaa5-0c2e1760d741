<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RecommendationServiceImpl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_class">RecommendationServiceImpl</span></div><h1>RecommendationServiceImpl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">138 of 580</td><td class="ctr2">76%</td><td class="bar">11 of 30</td><td class="ctr2">63%</td><td class="ctr1">15</td><td class="ctr2">33</td><td class="ctr1">23</td><td class="ctr2">125</td><td class="ctr1">6</td><td class="ctr2">18</td></tr></tfoot><tbody><tr><td id="a4"><a href="RecommendationServiceImpl.java.html#L114" class="el_method">getItemCFRecommendations(Long, int)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="79" alt="79"/><img src="../jacoco-resources/greenbar.gif" width="78" height="10" title="147" alt="147"/></td><td class="ctr2" id="c10">65%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="94" height="10" title="11" alt="11"/></td><td class="ctr2" id="e0">78%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h0">20</td><td class="ctr2" id="i0">56</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="RecommendationServiceImpl.java.html#L278" class="el_method">lambda$getContentBasedRecommendations$4(Map, Item, Item)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="18" alt="18"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="RecommendationServiceImpl.java.html#L214" class="el_method">getContentBasedRecommendations(Long, int)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="65" height="10" title="124" alt="124"/></td><td class="ctr2" id="c8">93%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="51" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">75%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i1">36</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a13"><a href="RecommendationServiceImpl.java.html#L187" class="el_method">lambda$getItemCFRecommendations$1(Set, Long)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="8" alt="8"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a6"><a href="RecommendationServiceImpl.java.html#L244" class="el_method">lambda$getContentBasedRecommendations$1(Item)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c11">50%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">25%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="RecommendationServiceImpl.java.html#L293" class="el_method">lambda$getContentBasedRecommendationsFallback$0()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a12"><a href="RecommendationServiceImpl.java.html#L186" class="el_method">lambda$getItemCFRecommendations$0(ItemResponse)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a8"><a href="RecommendationServiceImpl.java.html#L262" class="el_method">lambda$getContentBasedRecommendations$3(Set, Long)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c9">75%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="1" alt="1"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a15"><a href="RecommendationServiceImpl.java.html#L200" class="el_method">lambda$getItemCFRecommendations$3(Item, Item)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a14"><a href="RecommendationServiceImpl.java.html#L200" class="el_method">lambda$getItemCFRecommendations$2(Item)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a2"><a href="RecommendationServiceImpl.java.html#L289" class="el_method">getContentBasedRecommendationsFallback(Long, int)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="64" alt="64"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a3"><a href="RecommendationServiceImpl.java.html#L85" class="el_method">getHotRecommendations(int)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="52" alt="52"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a16"><a href="RecommendationServiceImpl.java.html#L73" class="el_method">RecommendationServiceImpl(ItemRepository, ItemSimilarityRepository, UserRepository, UserService, ItemService, JdbcTemplate)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="21" alt="21"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a7"><a href="RecommendationServiceImpl.java.html#L261" class="el_method">lambda$getContentBasedRecommendations$2(ItemResponse)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a5"><a href="RecommendationServiceImpl.java.html#L223" class="el_method">lambda$getContentBasedRecommendations$0()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a0"><a href="RecommendationServiceImpl.java.html#L324" class="el_method">calculateItemSimilarities()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i5">2</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a17"><a href="RecommendationServiceImpl.java.html#L39" class="el_method">static {...}</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a11"><a href="RecommendationServiceImpl.java.html#L101" class="el_method">lambda$getHotRecommendations$0(Item)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>