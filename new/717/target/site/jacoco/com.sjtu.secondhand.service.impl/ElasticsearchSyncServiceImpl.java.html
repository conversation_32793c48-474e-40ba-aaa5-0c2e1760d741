<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ElasticsearchSyncServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">ElasticsearchSyncServiceImpl.java</span></div><h1>ElasticsearchSyncServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.es.ItemDocument;
import com.sjtu.secondhand.repository.CategoryRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.es.ItemDocumentRepository;
import com.sjtu.secondhand.service.ElasticsearchSyncService;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ElasticsearchSyncServiceImpl implements ElasticsearchSyncService {

<span class="fc" id="L45">    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchSyncServiceImpl.class);</span>
    private static final String ITEMS_INDEX = &quot;items&quot;;

    private final ItemRepository itemRepository;
    private final ItemDocumentRepository itemDocumentRepository;
    private final CategoryRepository categoryRepository;
    private final ElasticsearchOperations elasticsearchOperations;
    private final RestTemplate restTemplate;
    
    @Value(&quot;${spring.elasticsearch.uris:http://localhost:9200}&quot;)
    private String elasticsearchUri;

    @Autowired
    public ElasticsearchSyncServiceImpl(ItemRepository itemRepository,
                                       ItemDocumentRepository itemDocumentRepository,
                                       CategoryRepository categoryRepository,
<span class="fc" id="L61">                                       ElasticsearchOperations elasticsearchOperations) {</span>
<span class="fc" id="L62">        this.itemRepository = itemRepository;</span>
<span class="fc" id="L63">        this.itemDocumentRepository = itemDocumentRepository;</span>
<span class="fc" id="L64">        this.categoryRepository = categoryRepository;</span>
<span class="fc" id="L65">        this.elasticsearchOperations = elasticsearchOperations;</span>
<span class="fc" id="L66">        this.restTemplate = new RestTemplate();</span>
<span class="fc" id="L67">        logger.info(&quot;ElasticsearchSyncServiceImpl初始化，待ES URI注入&quot;);</span>
<span class="fc" id="L68">    }</span>
    
    @PostConstruct
    public void init() {
<span class="fc" id="L72">        logger.info(&quot;ElasticsearchSyncServiceImpl初始化完成，ES URI: {}&quot;, elasticsearchUri);</span>
<span class="fc" id="L73">    }</span>

    @Async
    @Override
    public void syncItemToElasticsearch(Item item) {
        try {
<span class="fc bfc" id="L79" title="All 2 branches covered.">            if (item == null) {</span>
<span class="fc" id="L80">                logger.warn(&quot;尝试同步空物品到Elasticsearch&quot;);</span>
<span class="fc" id="L81">                return;</span>
            }
            
<span class="fc" id="L84">            logger.info(&quot;开始同步物品到Elasticsearch, 物品ID: {}&quot;, item.getId());</span>
<span class="fc" id="L85">            ItemDocument document = ItemDocument.fromItem(item);</span>
<span class="fc" id="L86">            itemDocumentRepository.save(document);</span>
<span class="fc" id="L87">            logger.info(&quot;物品同步到Elasticsearch成功, 物品ID: {}&quot;, item.getId());</span>
<span class="fc" id="L88">        } catch (Exception e) {</span>
<span class="fc" id="L89">            logger.error(&quot;物品同步到Elasticsearch失败, 物品ID: {}, 错误: {}&quot;, </span>
<span class="pc bpc" id="L90" title="1 of 2 branches missed.">                        item != null ? item.getId() : &quot;null&quot;, e.getMessage(), e);</span>
<span class="fc" id="L91">        }</span>
<span class="fc" id="L92">    }</span>

    @Async
    @Override
    public void deleteItemFromElasticsearch(Long itemId) {
        try {
<span class="fc" id="L98">            logger.info(&quot;开始从Elasticsearch删除物品, 物品ID: {}&quot;, itemId);</span>
<span class="fc" id="L99">            itemDocumentRepository.deleteById(itemId);</span>
<span class="fc" id="L100">            logger.info(&quot;从Elasticsearch删除物品成功, 物品ID: {}&quot;, itemId);</span>
<span class="fc" id="L101">        } catch (Exception e) {</span>
<span class="fc" id="L102">            logger.error(&quot;从Elasticsearch删除物品失败, 物品ID: {}, 错误: {}&quot;, itemId, e.getMessage(), e);</span>
<span class="fc" id="L103">        }</span>
<span class="fc" id="L104">    }</span>

    @Override
    public void syncAllItemsToElasticsearch() {
        try {
<span class="fc" id="L109">            logger.info(&quot;开始同步所有物品到Elasticsearch&quot;);</span>
            
            // 获取所有物品
<span class="fc" id="L112">            List&lt;Item&gt; allItems = itemRepository.findAll();</span>
<span class="fc" id="L113">            logger.info(&quot;从数据库获取到 {} 个物品准备同步&quot;, allItems.size());</span>
            
<span class="fc bfc" id="L115" title="All 2 branches covered.">            if (allItems.isEmpty()) {</span>
<span class="fc" id="L116">                logger.warn(&quot;数据库中没有物品可供同步&quot;);</span>
<span class="fc" id="L117">                return;</span>
            }
            
            // 先清空索引
            try {
<span class="fc" id="L122">                logger.info(&quot;尝试清空现有索引&quot;);</span>
<span class="fc" id="L123">                itemDocumentRepository.deleteAll();</span>
<span class="fc" id="L124">                logger.info(&quot;成功清空Elasticsearch索引&quot;);</span>
<span class="nc" id="L125">            } catch (Exception e) {</span>
<span class="nc" id="L126">                logger.warn(&quot;清空索引时出错: {}&quot;, e.getMessage());</span>
                // 继续执行，不中断同步过程
<span class="fc" id="L128">            }</span>
            
            // 准备文档列表
<span class="fc" id="L131">            List&lt;ItemDocument&gt; documents = new ArrayList&lt;&gt;();</span>
<span class="fc bfc" id="L132" title="All 2 branches covered.">            for (Item item : allItems) {</span>
                try {
<span class="fc" id="L134">                    ItemDocument doc = ItemDocument.fromItem(item);</span>
<span class="fc" id="L135">                    documents.add(doc);</span>
<span class="fc" id="L136">                    logger.debug(&quot;转换物品 ID: {} 为ES文档&quot;, item.getId());</span>
<span class="nc" id="L137">                } catch (Exception e) {</span>
<span class="nc" id="L138">                    logger.error(&quot;转换物品 ID: {} 为ES文档时出错: {}&quot;, item.getId(), e.getMessage());</span>
<span class="fc" id="L139">                }</span>
<span class="fc" id="L140">            }</span>
            
<span class="fc" id="L142">            logger.info(&quot;成功准备 {} 个文档，开始保存到Elasticsearch&quot;, documents.size());</span>
            
            // 批量保存
<span class="fc" id="L145">            Iterable&lt;ItemDocument&gt; saved = itemDocumentRepository.saveAll(documents);</span>
            
            // 统计保存结果
<span class="fc" id="L148">            long count = 0;</span>
<span class="fc bfc" id="L149" title="All 2 branches covered.">            for (ItemDocument doc : saved) {</span>
<span class="fc" id="L150">                count++;</span>
<span class="fc" id="L151">            }</span>
            
<span class="fc" id="L153">            logger.info(&quot;同步所有物品到Elasticsearch成功, 共同步 {} 个物品&quot;, count);</span>
<span class="fc" id="L154">        } catch (Exception e) {</span>
<span class="fc" id="L155">            logger.error(&quot;同步所有物品到Elasticsearch失败, 错误: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L156">            throw new RuntimeException(&quot;同步到Elasticsearch失败: &quot; + e.getMessage(), e);</span>
<span class="fc" id="L157">        }</span>
<span class="fc" id="L158">    }</span>

    @Override
    public Object searchItemsByKeyword(String keyword, int page, int size) {
        try {
<span class="fc" id="L163">            logger.info(&quot;开始从Elasticsearch搜索物品, 关键词: {}, 页码: {}, 大小: {}&quot;, keyword, page, size);</span>
            
            // 由于Spring Data Elasticsearch与ES 7.x版本兼容性问题
            // 返回一个带有标记的空结果，让控制器回退到MySQL搜索
<span class="fc" id="L167">            logger.info(&quot;由于Elasticsearch兼容性问题，回退到MySQL搜索&quot;);</span>
            
            // 创建一个空的结果集
<span class="fc" id="L170">            Map&lt;String, Object&gt; errorResponse = new HashMap&lt;&gt;();</span>
<span class="fc" id="L171">            errorResponse.put(&quot;items&quot;, new ArrayList&lt;&gt;());</span>
<span class="fc" id="L172">            errorResponse.put(&quot;total&quot;, 0);</span>
<span class="fc" id="L173">            errorResponse.put(&quot;page&quot;, page);</span>
<span class="fc" id="L174">            errorResponse.put(&quot;size&quot;, size);</span>
<span class="fc" id="L175">            errorResponse.put(&quot;source&quot;, &quot;elasticsearch_fallback&quot;);</span>
<span class="fc" id="L176">            errorResponse.put(&quot;message&quot;, &quot;Elasticsearch兼容性问题，自动回退到MySQL搜索&quot;);</span>
            
<span class="fc" id="L178">            return errorResponse;</span>
            
<span class="nc" id="L180">        } catch (Exception e) {</span>
<span class="nc" id="L181">            logger.error(&quot;从Elasticsearch搜索物品失败, 关键词: {}, 错误: {}&quot;, keyword, e.getMessage(), e);</span>
            
            // 创建一个空的结果集
<span class="nc" id="L184">            Map&lt;String, Object&gt; errorResponse = new HashMap&lt;&gt;();</span>
<span class="nc" id="L185">            errorResponse.put(&quot;items&quot;, new ArrayList&lt;&gt;());</span>
<span class="nc" id="L186">            errorResponse.put(&quot;total&quot;, 0);</span>
<span class="nc" id="L187">            errorResponse.put(&quot;page&quot;, page);</span>
<span class="nc" id="L188">            errorResponse.put(&quot;size&quot;, size);</span>
<span class="nc" id="L189">            errorResponse.put(&quot;source&quot;, &quot;elasticsearch_error&quot;);</span>
<span class="nc" id="L190">            errorResponse.put(&quot;error&quot;, &quot;搜索处理过程中出错: &quot; + e.getMessage());</span>
            
<span class="nc" id="L192">            return errorResponse;</span>
        }
    }

    @Override
    public Object advancedSearch(String keyword, Long categoryId, Double minPrice, Double maxPrice,
                               String itemType, String condition, int page, int size, String sort) {
        try {
<span class="fc" id="L200">            logger.info(&quot;开始从Elasticsearch高级搜索物品&quot;);</span>
            
            // 记录关键词处理
<span class="pc bpc" id="L203" title="2 of 4 branches missed.">            if (keyword != null &amp;&amp; !keyword.isEmpty()) {</span>
<span class="fc" id="L204">                logger.info(&quot;处理搜索关键词: {}&quot;, keyword);</span>
            }
            
            // 由于Spring Data Elasticsearch与ES 7.x版本兼容性问题
            // 返回一个带有标记的空结果，让控制器回退到MySQL搜索
<span class="fc" id="L209">            logger.info(&quot;由于Elasticsearch兼容性问题，回退到MySQL搜索&quot;);</span>
            
            // 创建一个空的结果集
<span class="fc" id="L212">            Map&lt;String, Object&gt; errorResponse = new HashMap&lt;&gt;();</span>
<span class="fc" id="L213">            errorResponse.put(&quot;items&quot;, new ArrayList&lt;&gt;());</span>
<span class="fc" id="L214">            errorResponse.put(&quot;total&quot;, 0);</span>
<span class="fc" id="L215">            errorResponse.put(&quot;page&quot;, page);</span>
<span class="fc" id="L216">            errorResponse.put(&quot;size&quot;, size);</span>
<span class="fc" id="L217">            errorResponse.put(&quot;source&quot;, &quot;elasticsearch_fallback&quot;);</span>
<span class="fc" id="L218">            errorResponse.put(&quot;message&quot;, &quot;Elasticsearch兼容性问题，自动回退到MySQL搜索&quot;);</span>
            
<span class="fc" id="L220">            return errorResponse;</span>
<span class="nc" id="L221">        } catch (Exception e) {</span>
<span class="nc" id="L222">            logger.error(&quot;从Elasticsearch高级搜索物品失败: {}&quot;, e.getMessage(), e);</span>
            
            // 创建一个空的结果集
<span class="nc" id="L225">            Map&lt;String, Object&gt; errorResponse = new HashMap&lt;&gt;();</span>
<span class="nc" id="L226">            errorResponse.put(&quot;items&quot;, new ArrayList&lt;&gt;());</span>
<span class="nc" id="L227">            errorResponse.put(&quot;total&quot;, 0);</span>
<span class="nc" id="L228">            errorResponse.put(&quot;page&quot;, page);</span>
<span class="nc" id="L229">            errorResponse.put(&quot;size&quot;, size);</span>
<span class="nc" id="L230">            errorResponse.put(&quot;source&quot;, &quot;elasticsearch_error&quot;);</span>
<span class="nc" id="L231">            errorResponse.put(&quot;error&quot;, &quot;搜索处理过程中出错: &quot; + e.getMessage());</span>
            
<span class="nc" id="L233">            return errorResponse;</span>
        }
    }
    
    @Override
    public Object directSearchByKeyword(String keyword, int page, int size) {
        try {
<span class="fc" id="L240">            logger.info(&quot;开始直接向ES发送请求搜索物品, 关键词: {}, 页码: {}, 大小: {}&quot;, keyword, page, size);</span>
<span class="fc" id="L241">            logger.info(&quot;使用的Elasticsearch URI: {}&quot;, elasticsearchUri);</span>
            
            // 构建请求体
<span class="fc" id="L244">            Map&lt;String, Object&gt; requestBody = new HashMap&lt;&gt;();</span>
            
            // 构建bool查询
<span class="fc" id="L247">            Map&lt;String, Object&gt; boolQuery = new HashMap&lt;&gt;();</span>
<span class="fc" id="L248">            List&lt;Map&lt;String, Object&gt;&gt; mustClauses = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L249">            List&lt;Map&lt;String, Object&gt;&gt; mustNotClauses = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L250">            List&lt;Map&lt;String, Object&gt;&gt; filterClauses = new ArrayList&lt;&gt;();</span>
            
            // 添加关键词搜索条件（must子句）
<span class="pc bpc" id="L253" title="2 of 4 branches missed.">            if (keyword != null &amp;&amp; !keyword.isEmpty()) {</span>
<span class="fc" id="L254">                Map&lt;String, Object&gt; multiMatch = new HashMap&lt;&gt;();</span>
<span class="fc" id="L255">                multiMatch.put(&quot;query&quot;, keyword);</span>
<span class="fc" id="L256">                multiMatch.put(&quot;fields&quot;, List.of(&quot;name^3&quot;, &quot;description&quot;));</span>
<span class="fc" id="L257">                multiMatch.put(&quot;analyzer&quot;, &quot;ik_smart_synonym&quot;);</span>
                
<span class="fc" id="L259">                Map&lt;String, Object&gt; multiMatchQuery = new HashMap&lt;&gt;();</span>
<span class="fc" id="L260">                multiMatchQuery.put(&quot;multi_match&quot;, multiMatch);</span>
<span class="fc" id="L261">                mustClauses.add(multiMatchQuery);</span>
            }
            
            // 添加可见性过滤（filter子句）
<span class="fc" id="L265">            Map&lt;String, Object&gt; visibleTerm = new HashMap&lt;&gt;();</span>
<span class="fc" id="L266">            visibleTerm.put(&quot;isVisible&quot;, true);</span>
            
<span class="fc" id="L268">            Map&lt;String, Object&gt; visibleQuery = new HashMap&lt;&gt;();</span>
<span class="fc" id="L269">            visibleQuery.put(&quot;term&quot;, visibleTerm);</span>
<span class="fc" id="L270">            filterClauses.add(visibleQuery);</span>
            
            // 添加状态过滤，只返回FOR_SALE状态的物品（filter子句）
<span class="fc" id="L273">            Map&lt;String, Object&gt; forSaleTerm = new HashMap&lt;&gt;();</span>
<span class="fc" id="L274">            forSaleTerm.put(&quot;status&quot;, &quot;FOR_SALE&quot;);</span>
            
<span class="fc" id="L276">            Map&lt;String, Object&gt; forSaleQuery = new HashMap&lt;&gt;();</span>
<span class="fc" id="L277">            forSaleQuery.put(&quot;term&quot;, forSaleTerm);</span>
<span class="fc" id="L278">            filterClauses.add(forSaleQuery);</span>
            
            // 组装bool查询
<span class="pc bpc" id="L281" title="1 of 2 branches missed.">            if (!mustClauses.isEmpty()) {</span>
<span class="fc" id="L282">                boolQuery.put(&quot;must&quot;, mustClauses);</span>
            }
            
<span class="pc bpc" id="L285" title="1 of 2 branches missed.">            if (!filterClauses.isEmpty()) {</span>
<span class="fc" id="L286">                boolQuery.put(&quot;filter&quot;, filterClauses);</span>
            }
            
<span class="pc bpc" id="L289" title="1 of 2 branches missed.">            if (!mustNotClauses.isEmpty()) {</span>
<span class="nc" id="L290">                boolQuery.put(&quot;must_not&quot;, mustNotClauses);</span>
            }
            
<span class="fc" id="L293">            Map&lt;String, Object&gt; query = new HashMap&lt;&gt;();</span>
<span class="fc" id="L294">            query.put(&quot;bool&quot;, boolQuery);</span>
<span class="fc" id="L295">            requestBody.put(&quot;query&quot;, query);</span>
            
            // 高亮部分
<span class="fc" id="L298">            Map&lt;String, Object&gt; nameHighlight = new HashMap&lt;&gt;();</span>
<span class="fc" id="L299">            Map&lt;String, Object&gt; descHighlight = new HashMap&lt;&gt;();</span>
<span class="fc" id="L300">            Map&lt;String, Object&gt; highlightFields = new HashMap&lt;&gt;();</span>
<span class="fc" id="L301">            highlightFields.put(&quot;name&quot;, nameHighlight);</span>
<span class="fc" id="L302">            highlightFields.put(&quot;description&quot;, descHighlight);</span>
            
<span class="fc" id="L304">            Map&lt;String, Object&gt; highlight = new HashMap&lt;&gt;();</span>
<span class="fc" id="L305">            highlight.put(&quot;fields&quot;, highlightFields);</span>
<span class="fc" id="L306">            requestBody.put(&quot;highlight&quot;, highlight);</span>
            
            // 分页
<span class="fc" id="L309">            requestBody.put(&quot;from&quot;, page * size);</span>
<span class="fc" id="L310">            requestBody.put(&quot;size&quot;, size);</span>
            
            // 排序
<span class="fc" id="L313">            Map&lt;String, Object&gt; scoreSort = new HashMap&lt;&gt;();</span>
<span class="fc" id="L314">            scoreSort.put(&quot;order&quot;, &quot;desc&quot;);</span>
            
<span class="fc" id="L316">            Map&lt;String, Object&gt; createdAtSort = new HashMap&lt;&gt;();</span>
<span class="fc" id="L317">            createdAtSort.put(&quot;order&quot;, &quot;desc&quot;);</span>
            
<span class="fc" id="L319">            List&lt;Map&lt;String, Object&gt;&gt; sorts = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L320">            Map&lt;String, Object&gt; sortScore = new HashMap&lt;&gt;();</span>
<span class="fc" id="L321">            sortScore.put(&quot;_score&quot;, scoreSort);</span>
<span class="fc" id="L322">            sorts.add(sortScore);</span>
            
<span class="fc" id="L324">            Map&lt;String, Object&gt; sortCreatedAt = new HashMap&lt;&gt;();</span>
<span class="fc" id="L325">            sortCreatedAt.put(&quot;createdAt&quot;, createdAtSort);</span>
<span class="fc" id="L326">            sorts.add(sortCreatedAt);</span>
            
<span class="fc" id="L328">            requestBody.put(&quot;sort&quot;, sorts);</span>
            
            // 设置HTTP头
<span class="fc" id="L331">            HttpHeaders headers = new HttpHeaders();</span>
<span class="fc" id="L332">            headers.setContentType(MediaType.APPLICATION_JSON);</span>
<span class="fc" id="L333">            HttpEntity&lt;Map&lt;String, Object&gt;&gt; requestEntity = new HttpEntity&lt;&gt;(requestBody, headers);</span>
            
            // 发送请求
<span class="fc" id="L336">            String searchUrl = elasticsearchUri + &quot;/&quot; + ITEMS_INDEX + &quot;/_search&quot;;</span>
<span class="fc" id="L337">            logger.info(&quot;发送ES搜索请求到: {}&quot;, searchUrl);</span>
<span class="fc" id="L338">            logger.info(&quot;请求体: {}&quot;, requestBody);</span>
            
            try {
<span class="fc" id="L341">                ResponseEntity&lt;Map&gt; response = restTemplate.postForEntity(searchUrl, requestEntity, Map.class);</span>
<span class="fc" id="L342">                logger.info(&quot;ES搜索请求完成，状态码: {}&quot;, response.getStatusCode());</span>
                
                // 处理响应
<span class="fc" id="L345">                return transformElasticsearchResponse(response.getBody(), page, size);</span>
<span class="fc" id="L346">            } catch (Exception e) {</span>
<span class="fc" id="L347">                logger.error(&quot;发送ES搜索请求失败: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L348">                throw e;</span>
            }
            
<span class="fc" id="L351">        } catch (Exception e) {</span>
<span class="fc" id="L352">            logger.error(&quot;直接向ES发送请求搜索物品失败, 关键词: {}, 错误: {}&quot;, keyword, e.getMessage(), e);</span>
            
            // 创建一个空的结果集
<span class="fc" id="L355">            Map&lt;String, Object&gt; errorResponse = new HashMap&lt;&gt;();</span>
<span class="fc" id="L356">            errorResponse.put(&quot;items&quot;, new ArrayList&lt;&gt;());</span>
<span class="fc" id="L357">            errorResponse.put(&quot;total&quot;, 0);</span>
<span class="fc" id="L358">            errorResponse.put(&quot;page&quot;, page);</span>
<span class="fc" id="L359">            errorResponse.put(&quot;size&quot;, size);</span>
<span class="fc" id="L360">            errorResponse.put(&quot;source&quot;, &quot;elasticsearch_error&quot;);</span>
<span class="fc" id="L361">            errorResponse.put(&quot;error&quot;, &quot;搜索处理过程中出错: &quot; + e.getMessage());</span>
            
<span class="fc" id="L363">            return errorResponse;</span>
        }
    }

    @Override
    public Object directAdvancedSearch(String keyword, Long categoryId, Double minPrice, Double maxPrice,
                               String itemType, String condition, int page, int size, String sort) {
        try {
<span class="fc" id="L371">            logger.info(&quot;开始直接向ES发送请求进行高级搜索&quot;);</span>
<span class="fc" id="L372">            logger.info(&quot;使用的Elasticsearch URI: {}&quot;, elasticsearchUri);</span>
<span class="fc" id="L373">            logger.info(&quot;搜索参数: 关键词={}, 分类ID={}, 价格范围=[{} - {}], 类型={}, 条件={}, 页码={}, 大小={}, 排序={}&quot;,</span>
<span class="fc" id="L374">                        keyword, categoryId, minPrice, maxPrice, itemType, condition, page, size, sort);</span>
            
            // 构建请求体
<span class="fc" id="L377">            Map&lt;String, Object&gt; requestBody = new HashMap&lt;&gt;();</span>
            
            // 创建bool查询
<span class="fc" id="L380">            Map&lt;String, Object&gt; boolQuery = new HashMap&lt;&gt;();</span>
<span class="fc" id="L381">            List&lt;Map&lt;String, Object&gt;&gt; mustClauses = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L382">            List&lt;Map&lt;String, Object&gt;&gt; mustNotClauses = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L383">            List&lt;Map&lt;String, Object&gt;&gt; filterClauses = new ArrayList&lt;&gt;();</span>
            
            // 添加关键词搜索条件（must子句）
<span class="pc bpc" id="L386" title="1 of 4 branches missed.">            if (keyword != null &amp;&amp; !keyword.isEmpty()) {</span>
<span class="fc" id="L387">                logger.info(&quot;处理搜索关键词: {}&quot;, keyword);</span>
<span class="fc" id="L388">                Map&lt;String, Object&gt; multiMatch = new HashMap&lt;&gt;();</span>
<span class="fc" id="L389">                multiMatch.put(&quot;query&quot;, keyword);</span>
<span class="fc" id="L390">                multiMatch.put(&quot;fields&quot;, List.of(&quot;name^3&quot;, &quot;description&quot;));</span>
<span class="fc" id="L391">                multiMatch.put(&quot;analyzer&quot;, &quot;ik_smart_synonym&quot;);</span>
                
<span class="fc" id="L393">                Map&lt;String, Object&gt; multiMatchQuery = new HashMap&lt;&gt;();</span>
<span class="fc" id="L394">                multiMatchQuery.put(&quot;multi_match&quot;, multiMatch);</span>
<span class="fc" id="L395">                mustClauses.add(multiMatchQuery);</span>
            }
            
            // 添加分类过滤（filter子句）
<span class="fc bfc" id="L399" title="All 2 branches covered.">            if (categoryId != null) {</span>
<span class="fc" id="L400">                Map&lt;String, Object&gt; termValue = new HashMap&lt;&gt;();</span>
<span class="fc" id="L401">                termValue.put(&quot;category.id&quot;, categoryId);</span>
                
<span class="fc" id="L403">                Map&lt;String, Object&gt; term = new HashMap&lt;&gt;();</span>
<span class="fc" id="L404">                term.put(&quot;term&quot;, termValue);</span>
<span class="fc" id="L405">                filterClauses.add(term);</span>
            }
            
            // 添加价格范围过滤（filter子句）
<span class="pc bpc" id="L409" title="1 of 4 branches missed.">            if (minPrice != null || maxPrice != null) {</span>
<span class="fc" id="L410">                Map&lt;String, Object&gt; rangeValues = new HashMap&lt;&gt;();</span>
                
<span class="pc bpc" id="L412" title="1 of 2 branches missed.">                if (minPrice != null) {</span>
<span class="fc" id="L413">                    rangeValues.put(&quot;gte&quot;, minPrice);</span>
                }
                
<span class="pc bpc" id="L416" title="1 of 2 branches missed.">                if (maxPrice != null) {</span>
<span class="fc" id="L417">                    rangeValues.put(&quot;lte&quot;, maxPrice);</span>
                }
                
<span class="fc" id="L420">                Map&lt;String, Object&gt; priceRange = new HashMap&lt;&gt;();</span>
<span class="fc" id="L421">                priceRange.put(&quot;price&quot;, rangeValues);</span>
                
<span class="fc" id="L423">                Map&lt;String, Object&gt; range = new HashMap&lt;&gt;();</span>
<span class="fc" id="L424">                range.put(&quot;range&quot;, priceRange);</span>
<span class="fc" id="L425">                filterClauses.add(range);</span>
            }
            
            // 添加物品类型过滤（filter子句）
<span class="pc bpc" id="L429" title="1 of 4 branches missed.">            if (itemType != null &amp;&amp; !itemType.isEmpty()) {</span>
<span class="fc" id="L430">                Map&lt;String, Object&gt; termValue = new HashMap&lt;&gt;();</span>
<span class="fc" id="L431">                termValue.put(&quot;itemType&quot;, itemType);</span>
                
<span class="fc" id="L433">                Map&lt;String, Object&gt; term = new HashMap&lt;&gt;();</span>
<span class="fc" id="L434">                term.put(&quot;term&quot;, termValue);</span>
<span class="fc" id="L435">                filterClauses.add(term);</span>
            }
            
            // 添加物品状况过滤（filter子句）
<span class="pc bpc" id="L439" title="1 of 4 branches missed.">            if (condition != null &amp;&amp; !condition.isEmpty()) {</span>
<span class="fc" id="L440">                Map&lt;String, Object&gt; termValue = new HashMap&lt;&gt;();</span>
<span class="fc" id="L441">                termValue.put(&quot;condition&quot;, condition);</span>
                
<span class="fc" id="L443">                Map&lt;String, Object&gt; term = new HashMap&lt;&gt;();</span>
<span class="fc" id="L444">                term.put(&quot;term&quot;, termValue);</span>
<span class="fc" id="L445">                filterClauses.add(term);</span>
            }
            
            // 添加可见性过滤（filter子句）
<span class="fc" id="L449">            Map&lt;String, Object&gt; visibleTerm = new HashMap&lt;&gt;();</span>
<span class="fc" id="L450">            visibleTerm.put(&quot;isVisible&quot;, true);</span>
            
<span class="fc" id="L452">            Map&lt;String, Object&gt; visibleQuery = new HashMap&lt;&gt;();</span>
<span class="fc" id="L453">            visibleQuery.put(&quot;term&quot;, visibleTerm);</span>
<span class="fc" id="L454">            filterClauses.add(visibleQuery);</span>
            
            // 添加状态过滤，只返回FOR_SALE状态的物品（filter子句）
<span class="fc" id="L457">            Map&lt;String, Object&gt; forSaleTerm = new HashMap&lt;&gt;();</span>
<span class="fc" id="L458">            forSaleTerm.put(&quot;status&quot;, &quot;FOR_SALE&quot;);</span>
            
<span class="fc" id="L460">            Map&lt;String, Object&gt; forSaleQuery = new HashMap&lt;&gt;();</span>
<span class="fc" id="L461">            forSaleQuery.put(&quot;term&quot;, forSaleTerm);</span>
<span class="fc" id="L462">            filterClauses.add(forSaleQuery);</span>
            
            // 组装bool查询
<span class="fc bfc" id="L465" title="All 2 branches covered.">            if (!mustClauses.isEmpty()) {</span>
<span class="fc" id="L466">                boolQuery.put(&quot;must&quot;, mustClauses);</span>
            }
            
<span class="pc bpc" id="L469" title="1 of 2 branches missed.">            if (!filterClauses.isEmpty()) {</span>
<span class="fc" id="L470">                boolQuery.put(&quot;filter&quot;, filterClauses);</span>
            }
            
<span class="pc bpc" id="L473" title="1 of 2 branches missed.">            if (!mustNotClauses.isEmpty()) {</span>
<span class="nc" id="L474">                boolQuery.put(&quot;must_not&quot;, mustNotClauses);</span>
            }
            
<span class="fc" id="L477">            Map&lt;String, Object&gt; query = new HashMap&lt;&gt;();</span>
<span class="fc" id="L478">            query.put(&quot;bool&quot;, boolQuery);</span>
<span class="fc" id="L479">            requestBody.put(&quot;query&quot;, query);</span>
            
            // 高亮部分
<span class="fc" id="L482">            Map&lt;String, Object&gt; nameHighlight = new HashMap&lt;&gt;();</span>
<span class="fc" id="L483">            Map&lt;String, Object&gt; descHighlight = new HashMap&lt;&gt;();</span>
<span class="fc" id="L484">            Map&lt;String, Object&gt; highlightFields = new HashMap&lt;&gt;();</span>
<span class="fc" id="L485">            highlightFields.put(&quot;name&quot;, nameHighlight);</span>
<span class="fc" id="L486">            highlightFields.put(&quot;description&quot;, descHighlight);</span>
            
<span class="fc" id="L488">            Map&lt;String, Object&gt; highlight = new HashMap&lt;&gt;();</span>
<span class="fc" id="L489">            highlight.put(&quot;fields&quot;, highlightFields);</span>
<span class="fc" id="L490">            requestBody.put(&quot;highlight&quot;, highlight);</span>
            
            // 分页
<span class="fc" id="L493">            requestBody.put(&quot;from&quot;, page * size);</span>
<span class="fc" id="L494">            requestBody.put(&quot;size&quot;, size);</span>
            
            // 排序
<span class="fc" id="L497">            List&lt;Map&lt;String, Object&gt;&gt; sorts = new ArrayList&lt;&gt;();</span>
            
            // 默认按照相关性和时间排序
<span class="fc bfc" id="L500" title="All 6 branches covered.">            if (sort == null || sort.isEmpty() || &quot;relevance&quot;.equals(sort)) {</span>
<span class="fc" id="L501">                Map&lt;String, Object&gt; scoreSort = new HashMap&lt;&gt;();</span>
<span class="fc" id="L502">                scoreSort.put(&quot;order&quot;, &quot;desc&quot;);</span>
                
<span class="fc" id="L504">                Map&lt;String, Object&gt; sortScore = new HashMap&lt;&gt;();</span>
<span class="fc" id="L505">                sortScore.put(&quot;_score&quot;, scoreSort);</span>
<span class="fc" id="L506">                sorts.add(sortScore);</span>
                
<span class="fc" id="L508">                Map&lt;String, Object&gt; createdAtSort = new HashMap&lt;&gt;();</span>
<span class="fc" id="L509">                createdAtSort.put(&quot;order&quot;, &quot;desc&quot;);</span>
                
<span class="fc" id="L511">                Map&lt;String, Object&gt; sortCreatedAt = new HashMap&lt;&gt;();</span>
<span class="fc" id="L512">                sortCreatedAt.put(&quot;createdAt&quot;, createdAtSort);</span>
<span class="fc" id="L513">                sorts.add(sortCreatedAt);</span>
<span class="fc bfc" id="L514" title="All 2 branches covered.">            } else if (&quot;price_asc&quot;.equals(sort)) {</span>
<span class="fc" id="L515">                Map&lt;String, Object&gt; priceSort = new HashMap&lt;&gt;();</span>
<span class="fc" id="L516">                priceSort.put(&quot;order&quot;, &quot;asc&quot;);</span>
                
<span class="fc" id="L518">                Map&lt;String, Object&gt; sortPrice = new HashMap&lt;&gt;();</span>
<span class="fc" id="L519">                sortPrice.put(&quot;price&quot;, priceSort);</span>
<span class="fc" id="L520">                sorts.add(sortPrice);</span>
<span class="fc bfc" id="L521" title="All 2 branches covered.">            } else if (&quot;price_desc&quot;.equals(sort)) {</span>
<span class="fc" id="L522">                Map&lt;String, Object&gt; priceSort = new HashMap&lt;&gt;();</span>
<span class="fc" id="L523">                priceSort.put(&quot;order&quot;, &quot;desc&quot;);</span>
                
<span class="fc" id="L525">                Map&lt;String, Object&gt; sortPrice = new HashMap&lt;&gt;();</span>
<span class="fc" id="L526">                sortPrice.put(&quot;price&quot;, priceSort);</span>
<span class="fc" id="L527">                sorts.add(sortPrice);</span>
<span class="fc bfc" id="L528" title="All 2 branches covered.">            } else if (&quot;date_desc&quot;.equals(sort)) {</span>
<span class="fc" id="L529">                Map&lt;String, Object&gt; dateSort = new HashMap&lt;&gt;();</span>
<span class="fc" id="L530">                dateSort.put(&quot;order&quot;, &quot;desc&quot;);</span>
                
<span class="fc" id="L532">                Map&lt;String, Object&gt; sortDate = new HashMap&lt;&gt;();</span>
<span class="fc" id="L533">                sortDate.put(&quot;createdAt&quot;, dateSort);</span>
<span class="fc" id="L534">                sorts.add(sortDate);</span>
<span class="pc bpc" id="L535" title="1 of 2 branches missed.">            } else if (&quot;date_asc&quot;.equals(sort)) {</span>
<span class="fc" id="L536">                Map&lt;String, Object&gt; dateSort = new HashMap&lt;&gt;();</span>
<span class="fc" id="L537">                dateSort.put(&quot;order&quot;, &quot;asc&quot;);</span>
                
<span class="fc" id="L539">                Map&lt;String, Object&gt; sortDate = new HashMap&lt;&gt;();</span>
<span class="fc" id="L540">                sortDate.put(&quot;createdAt&quot;, dateSort);</span>
<span class="fc" id="L541">                sorts.add(sortDate);</span>
            }
            
<span class="fc" id="L544">            requestBody.put(&quot;sort&quot;, sorts);</span>
            
            // 设置HTTP头
<span class="fc" id="L547">            HttpHeaders headers = new HttpHeaders();</span>
<span class="fc" id="L548">            headers.setContentType(MediaType.APPLICATION_JSON);</span>
<span class="fc" id="L549">            HttpEntity&lt;Map&lt;String, Object&gt;&gt; requestEntity = new HttpEntity&lt;&gt;(requestBody, headers);</span>
            
            // 发送请求
<span class="fc" id="L552">            String searchUrl = elasticsearchUri + &quot;/&quot; + ITEMS_INDEX + &quot;/_search&quot;;</span>
<span class="fc" id="L553">            logger.info(&quot;发送ES高级搜索请求到: {}&quot;, searchUrl);</span>
<span class="fc" id="L554">            logger.info(&quot;请求体: {}&quot;, requestBody);</span>
            
            try {
<span class="fc" id="L557">                ResponseEntity&lt;Map&gt; response = restTemplate.postForEntity(searchUrl, requestEntity, Map.class);</span>
<span class="fc" id="L558">                logger.info(&quot;ES高级搜索请求完成，状态码: {}&quot;, response.getStatusCode());</span>
                
                // 处理响应
<span class="fc" id="L561">                return transformElasticsearchResponse(response.getBody(), page, size);</span>
<span class="fc" id="L562">            } catch (Exception e) {</span>
<span class="fc" id="L563">                logger.error(&quot;发送ES高级搜索请求失败: {}&quot;, e.getMessage(), e);</span>
<span class="fc" id="L564">                throw e;</span>
            }
            
<span class="fc" id="L567">        } catch (Exception e) {</span>
<span class="fc" id="L568">            logger.error(&quot;直接向ES发送请求高级搜索物品失败: {}&quot;, e.getMessage(), e);</span>
            
            // 创建一个空的结果集
<span class="fc" id="L571">            Map&lt;String, Object&gt; errorResponse = new HashMap&lt;&gt;();</span>
<span class="fc" id="L572">            errorResponse.put(&quot;items&quot;, new ArrayList&lt;&gt;());</span>
<span class="fc" id="L573">            errorResponse.put(&quot;total&quot;, 0);</span>
<span class="fc" id="L574">            errorResponse.put(&quot;page&quot;, page);</span>
<span class="fc" id="L575">            errorResponse.put(&quot;size&quot;, size);</span>
<span class="fc" id="L576">            errorResponse.put(&quot;source&quot;, &quot;elasticsearch_error&quot;);</span>
<span class="fc" id="L577">            errorResponse.put(&quot;error&quot;, &quot;高级搜索处理过程中出错: &quot; + e.getMessage());</span>
            
<span class="fc" id="L579">            return errorResponse;</span>
        }
    }
    
    // 处理ES返回的结果
    @SuppressWarnings(&quot;unchecked&quot;)
    private Map&lt;String, Object&gt; transformElasticsearchResponse(Map&lt;String, Object&gt; esResponse, int page, int size) {
<span class="fc" id="L586">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
        
<span class="fc bfc" id="L588" title="All 2 branches covered.">        if (esResponse == null) {</span>
<span class="fc" id="L589">            logger.warn(&quot;ES返回空响应&quot;);</span>
<span class="fc" id="L590">            result.put(&quot;items&quot;, new ArrayList&lt;&gt;());</span>
<span class="fc" id="L591">            result.put(&quot;total&quot;, 0);</span>
<span class="fc" id="L592">            result.put(&quot;page&quot;, page);</span>
<span class="fc" id="L593">            result.put(&quot;size&quot;, size);</span>
<span class="fc" id="L594">            result.put(&quot;source&quot;, &quot;elasticsearch&quot;);</span>
<span class="fc" id="L595">            result.put(&quot;error&quot;, &quot;ES返回空响应&quot;);</span>
<span class="fc" id="L596">            return result;</span>
        }
        
        try {
            // 记录原始响应中的关键信息
<span class="fc" id="L601">            logger.info(&quot;ES响应 took: {}, timed_out: {}&quot;, </span>
<span class="fc" id="L602">                       esResponse.get(&quot;took&quot;), esResponse.get(&quot;timed_out&quot;));</span>
            
<span class="pc bpc" id="L604" title="1 of 2 branches missed.">            if (esResponse.containsKey(&quot;_shards&quot;)) {</span>
<span class="fc" id="L605">                Map&lt;String, Object&gt; shards = (Map&lt;String, Object&gt;) esResponse.get(&quot;_shards&quot;);</span>
<span class="fc" id="L606">                logger.info(&quot;ES响应 _shards: total={}, successful={}, failed={}&quot;, </span>
<span class="fc" id="L607">                           shards.get(&quot;total&quot;), shards.get(&quot;successful&quot;), shards.get(&quot;failed&quot;));</span>
            }
            
            // 从ES响应中提取hits
<span class="fc" id="L611">            Map&lt;String, Object&gt; hitsContainer = (Map&lt;String, Object&gt;) esResponse.get(&quot;hits&quot;);</span>
            
<span class="pc bpc" id="L613" title="1 of 2 branches missed.">            if (hitsContainer == null) {</span>
<span class="nc" id="L614">                logger.warn(&quot;ES响应中无hits部分&quot;);</span>
<span class="nc" id="L615">                result.put(&quot;items&quot;, new ArrayList&lt;&gt;());</span>
<span class="nc" id="L616">                result.put(&quot;total&quot;, 0);</span>
<span class="nc" id="L617">                result.put(&quot;page&quot;, page);</span>
<span class="nc" id="L618">                result.put(&quot;size&quot;, size);</span>
<span class="nc" id="L619">                result.put(&quot;source&quot;, &quot;elasticsearch&quot;);</span>
<span class="nc" id="L620">                return result;</span>
            }
            
            // 获取总数
<span class="fc" id="L624">            Map&lt;String, Object&gt; totalMap = (Map&lt;String, Object&gt;) hitsContainer.get(&quot;total&quot;);</span>
<span class="pc bpc" id="L625" title="1 of 2 branches missed.">            int total = totalMap != null ? ((Number) totalMap.get(&quot;value&quot;)).intValue() : 0;</span>
<span class="fc" id="L626">            logger.info(&quot;ES搜索结果总数: {}&quot;, total);</span>
            
            // 获取命中的文档
<span class="fc" id="L629">            List&lt;Map&lt;String, Object&gt;&gt; hitsList = (List&lt;Map&lt;String, Object&gt;&gt;) hitsContainer.get(&quot;hits&quot;);</span>
<span class="pc bpc" id="L630" title="1 of 2 branches missed.">            logger.info(&quot;ES搜索结果条数: {}&quot;, hitsList != null ? hitsList.size() : 0);</span>
            
            // 转换为ItemResponse格式
<span class="fc" id="L633">            List&lt;Map&lt;String, Object&gt;&gt; items = new ArrayList&lt;&gt;();</span>
            
<span class="pc bpc" id="L635" title="1 of 2 branches missed.">            if (hitsList != null) {</span>
<span class="fc bfc" id="L636" title="All 2 branches covered.">                for (Map&lt;String, Object&gt; hit : hitsList) {</span>
<span class="fc" id="L637">                    Map&lt;String, Object&gt; source = (Map&lt;String, Object&gt;) hit.get(&quot;_source&quot;);</span>
                    
<span class="pc bpc" id="L639" title="1 of 2 branches missed.">                    if (source != null) {</span>
                        // 处理高亮显示
<span class="fc" id="L641">                        Map&lt;String, Object&gt; highlight = (Map&lt;String, Object&gt;) hit.get(&quot;highlight&quot;);</span>
<span class="fc bfc" id="L642" title="All 2 branches covered.">                        if (highlight != null) {</span>
<span class="fc" id="L643">                            List&lt;String&gt; nameHighlights = (List&lt;String&gt;) highlight.get(&quot;name&quot;);</span>
<span class="pc bpc" id="L644" title="2 of 4 branches missed.">                            if (nameHighlights != null &amp;&amp; !nameHighlights.isEmpty()) {</span>
<span class="fc" id="L645">                                source.put(&quot;nameHighlight&quot;, nameHighlights.get(0));</span>
                            }
                            
<span class="fc" id="L648">                            List&lt;String&gt; descHighlights = (List&lt;String&gt;) highlight.get(&quot;description&quot;);</span>
<span class="pc bpc" id="L649" title="2 of 4 branches missed.">                            if (descHighlights != null &amp;&amp; !descHighlights.isEmpty()) {</span>
<span class="fc" id="L650">                                source.put(&quot;descriptionHighlight&quot;, descHighlights.get(0));</span>
                            }
                        }
                        
                        // 添加相关性得分
<span class="fc" id="L655">                        double score = ((Number) hit.get(&quot;_score&quot;)).doubleValue();</span>
<span class="fc" id="L656">                        source.put(&quot;score&quot;, score);</span>
                        
                        // 记录文档ID和得分
<span class="fc" id="L659">                        logger.debug(&quot;处理命中文档: id={}, score={}&quot;, hit.get(&quot;_id&quot;), score);</span>
                        
                        // =========== 添加前端期望的字段 ===========
                        // 1. 处理图片 - 为保持兼容性，同时保留imageUrls字段
<span class="fc" id="L663">                        List&lt;String&gt; imageUrls = (List&lt;String&gt;) source.get(&quot;imageUrls&quot;);</span>
<span class="pc bpc" id="L664" title="2 of 4 branches missed.">                        if (imageUrls != null &amp;&amp; !imageUrls.isEmpty()) {</span>
                            // 普通搜索中images是字符串数组，不是对象数组
<span class="fc" id="L666">                            source.put(&quot;images&quot;, imageUrls);</span>
                            
                            // 删除ES特有的图片字段，避免前端混淆
<span class="fc" id="L669">                            source.remove(&quot;mainImage&quot;);</span>
                            
<span class="fc" id="L671">                            logger.debug(&quot;添加图片信息: images数量={}&quot;, imageUrls.size());</span>
                        } else {
                            // 无图片时设置空列表
<span class="nc" id="L674">                            source.put(&quot;images&quot;, new ArrayList&lt;&gt;());</span>
                        }
                        
                        // 2. 处理用户信息 - 确保同时有user和seller字段
<span class="fc" id="L678">                        Map&lt;String, Object&gt; user = (Map&lt;String, Object&gt;) source.get(&quot;user&quot;);</span>
<span class="pc bpc" id="L679" title="1 of 2 branches missed.">                        if (user != null) {</span>
                            // 确保user中使用avatar字段而不是avatarUrl
<span class="fc bfc" id="L681" title="All 2 branches covered.">                            if (user.containsKey(&quot;avatarUrl&quot;)) {</span>
<span class="fc" id="L682">                                user.put(&quot;avatar&quot;, user.get(&quot;avatarUrl&quot;));</span>
<span class="fc" id="L683">                                user.remove(&quot;avatarUrl&quot;); // 移除avatarUrl字段，避免前端混淆</span>
                            }
                            
                            // 确保存在seller字段
<span class="fc bfc" id="L687" title="All 2 branches covered.">                            if (!source.containsKey(&quot;seller&quot;)) {</span>
<span class="fc" id="L688">                                Map&lt;String, Object&gt; seller = new HashMap&lt;&gt;(user);</span>
<span class="fc" id="L689">                                source.put(&quot;seller&quot;, seller);</span>
<span class="fc" id="L690">                            } else {</span>
                                // 如果已有seller字段，确保其中使用avatar
<span class="fc" id="L692">                                Map&lt;String, Object&gt; seller = (Map&lt;String, Object&gt;) source.get(&quot;seller&quot;);</span>
<span class="pc bpc" id="L693" title="1 of 2 branches missed.">                                if (seller.containsKey(&quot;avatarUrl&quot;)) {</span>
<span class="nc" id="L694">                                    seller.put(&quot;avatar&quot;, seller.get(&quot;avatarUrl&quot;));</span>
<span class="nc" id="L695">                                    seller.remove(&quot;avatarUrl&quot;); // 移除avatarUrl字段，避免前端混淆</span>
                                }
                            }
                        }
                        
                        // 3. 确保title字段存在（与name相同）
<span class="pc bpc" id="L701" title="1 of 4 branches missed.">                        if (!source.containsKey(&quot;title&quot;) &amp;&amp; source.containsKey(&quot;name&quot;)) {</span>
<span class="fc" id="L702">                            source.put(&quot;title&quot;, source.get(&quot;name&quot;));</span>
                        }
                        
                        // 4. 添加普通搜索中的其他字段
<span class="fc bfc" id="L706" title="All 2 branches covered.">                        if (!source.containsKey(&quot;priceMin&quot;)) {</span>
<span class="fc" id="L707">                            source.put(&quot;priceMin&quot;, null);</span>
                        }
<span class="fc bfc" id="L709" title="All 2 branches covered.">                        if (!source.containsKey(&quot;priceMax&quot;)) {</span>
<span class="fc" id="L710">                            source.put(&quot;priceMax&quot;, null);</span>
                        }
<span class="pc bpc" id="L712" title="1 of 4 branches missed.">                        if (!source.containsKey(&quot;categoryId&quot;) &amp;&amp; source.containsKey(&quot;category&quot;)) {</span>
<span class="fc" id="L713">                            Map&lt;String, Object&gt; category = (Map&lt;String, Object&gt;) source.get(&quot;category&quot;);</span>
<span class="pc bpc" id="L714" title="2 of 4 branches missed.">                            if (category != null &amp;&amp; category.containsKey(&quot;id&quot;)) {</span>
<span class="fc" id="L715">                                source.put(&quot;categoryId&quot;, category.get(&quot;id&quot;));</span>
                            }
                        }
<span class="pc bpc" id="L718" title="1 of 4 branches missed.">                        if (!source.containsKey(&quot;categoryName&quot;) &amp;&amp; source.containsKey(&quot;category&quot;)) {</span>
<span class="fc" id="L719">                            Map&lt;String, Object&gt; category = (Map&lt;String, Object&gt;) source.get(&quot;category&quot;);</span>
<span class="pc bpc" id="L720" title="2 of 4 branches missed.">                            if (category != null &amp;&amp; category.containsKey(&quot;name&quot;)) {</span>
<span class="fc" id="L721">                                source.put(&quot;categoryName&quot;, category.get(&quot;name&quot;));</span>
                            }
                        }
<span class="fc bfc" id="L724" title="All 2 branches covered.">                        if (!source.containsKey(&quot;isFavorited&quot;)) {</span>
<span class="fc" id="L725">                            source.put(&quot;isFavorited&quot;, false);</span>
                        }
                        
                        // 5. 格式化日期字段
<span class="pc bpc" id="L729" title="3 of 4 branches missed.">                        if (source.containsKey(&quot;createdAt&quot;) &amp;&amp; !(source.get(&quot;createdAt&quot;) instanceof String)) {</span>
                            try {
<span class="nc" id="L731">                                String createdAt = source.get(&quot;createdAt&quot;).toString();</span>
<span class="nc bnc" id="L732" title="All 2 branches missed.">                                if (!createdAt.contains(&quot; &quot;)) {</span>
<span class="nc" id="L733">                                    source.put(&quot;createdAt&quot;, createdAt + &quot; 00:00:00&quot;);</span>
                                }
<span class="nc" id="L735">                            } catch (Exception e) {</span>
<span class="nc" id="L736">                                logger.warn(&quot;格式化createdAt字段失败: {}&quot;, e.getMessage());</span>
<span class="nc" id="L737">                            }</span>
                        }
<span class="pc bpc" id="L739" title="3 of 4 branches missed.">                        if (source.containsKey(&quot;updatedAt&quot;) &amp;&amp; !(source.get(&quot;updatedAt&quot;) instanceof String)) {</span>
                            try {
<span class="nc" id="L741">                                String updatedAt = source.get(&quot;updatedAt&quot;).toString();</span>
<span class="nc bnc" id="L742" title="All 2 branches missed.">                                if (!updatedAt.contains(&quot; &quot;)) {</span>
<span class="nc" id="L743">                                    source.put(&quot;updatedAt&quot;, updatedAt + &quot; 00:00:00&quot;);</span>
                                }
<span class="nc" id="L745">                            } catch (Exception e) {</span>
<span class="nc" id="L746">                                logger.warn(&quot;格式化updatedAt字段失败: {}&quot;, e.getMessage());</span>
<span class="nc" id="L747">                            }</span>
                        }
                        
                        // 6. 添加viewTime和viewTimeRaw字段
<span class="fc bfc" id="L751" title="All 2 branches covered.">                        if (!source.containsKey(&quot;viewTime&quot;)) {</span>
<span class="fc" id="L752">                            source.put(&quot;viewTime&quot;, null);</span>
                        }
<span class="fc bfc" id="L754" title="All 2 branches covered.">                        if (!source.containsKey(&quot;viewTimeRaw&quot;)) {</span>
<span class="fc" id="L755">                            source.put(&quot;viewTimeRaw&quot;, null);</span>
                        }
                        
                        // 7. 删除ES特有的字段，避免前端混淆
<span class="fc" id="L759">                        source.remove(&quot;_class&quot;);</span>
<span class="fc" id="L760">                        source.remove(&quot;isVisible&quot;);</span>
<span class="fc" id="L761">                        source.remove(&quot;nameHighlight&quot;);</span>
<span class="fc" id="L762">                        source.remove(&quot;descriptionHighlight&quot;);</span>
<span class="fc" id="L763">                        source.remove(&quot;score&quot;);</span>
                        
<span class="fc" id="L765">                        items.add(source);</span>
                    }
<span class="fc" id="L767">                }</span>
            }
            
            // 填充结果
<span class="fc" id="L771">            result.put(&quot;items&quot;, items);</span>
<span class="fc" id="L772">            result.put(&quot;total&quot;, total);</span>
<span class="fc" id="L773">            result.put(&quot;page&quot;, page);</span>
<span class="fc" id="L774">            result.put(&quot;size&quot;, size);</span>
<span class="fc" id="L775">            result.put(&quot;source&quot;, &quot;elasticsearch_direct&quot;);</span>
<span class="fc" id="L776">            result.put(&quot;took&quot;, esResponse.get(&quot;took&quot;));</span>
            
<span class="fc" id="L778">            logger.info(&quot;转换ES响应完成，返回 {} 个物品&quot;, items.size());</span>
<span class="fc" id="L779">            return result;</span>
            
<span class="nc" id="L781">        } catch (Exception e) {</span>
<span class="nc" id="L782">            logger.error(&quot;处理ES响应时出错: {}&quot;, e.getMessage(), e);</span>
            
<span class="nc" id="L784">            result.put(&quot;items&quot;, new ArrayList&lt;&gt;());</span>
<span class="nc" id="L785">            result.put(&quot;total&quot;, 0);</span>
<span class="nc" id="L786">            result.put(&quot;page&quot;, page);</span>
<span class="nc" id="L787">            result.put(&quot;size&quot;, size);</span>
<span class="nc" id="L788">            result.put(&quot;source&quot;, &quot;elasticsearch_error&quot;);</span>
<span class="nc" id="L789">            result.put(&quot;error&quot;, &quot;处理ES响应时出错: &quot; + e.getMessage());</span>
            
<span class="nc" id="L791">            return result;</span>
        }
    }
    
    // 辅助方法：将ItemDocument转换为Map
    private Map&lt;String, Object&gt; convertToMap(ItemDocument document) {
<span class="nc" id="L797">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L798">        result.put(&quot;id&quot;, document.getId());</span>
<span class="nc" id="L799">        result.put(&quot;name&quot;, document.getName());</span>
<span class="nc" id="L800">        result.put(&quot;title&quot;, document.getName()); // 为了兼容性</span>
<span class="nc" id="L801">        result.put(&quot;description&quot;, document.getDescription());</span>
<span class="nc" id="L802">        result.put(&quot;price&quot;, document.getPrice());</span>
<span class="nc" id="L803">        result.put(&quot;priceMin&quot;, document.getPriceMin());</span>
<span class="nc" id="L804">        result.put(&quot;priceMax&quot;, document.getPriceMax());</span>
<span class="nc" id="L805">        result.put(&quot;itemType&quot;, document.getItemType());</span>
<span class="nc" id="L806">        result.put(&quot;condition&quot;, document.getCondition());</span>
<span class="nc" id="L807">        result.put(&quot;status&quot;, document.getStatus());</span>
<span class="nc" id="L808">        result.put(&quot;viewCount&quot;, document.getViewCount());</span>
<span class="nc" id="L809">        result.put(&quot;favoriteCount&quot;, document.getFavoriteCount());</span>
<span class="nc" id="L810">        result.put(&quot;createdAt&quot;, document.getCreatedAt());</span>
<span class="nc" id="L811">        result.put(&quot;updatedAt&quot;, document.getUpdatedAt());</span>
<span class="nc" id="L812">        result.put(&quot;category&quot;, document.getCategory());</span>
<span class="nc" id="L813">        result.put(&quot;seller&quot;, document.getUser()); // 为了兼容性</span>
<span class="nc" id="L814">        result.put(&quot;user&quot;, document.getUser());</span>
<span class="nc" id="L815">        result.put(&quot;images&quot;, document.getImageUrls());</span>
        
<span class="nc" id="L817">        return result;</span>
    }

    // 辅助方法：将Item转换为Map
    private Map&lt;String, Object&gt; convertItemToMap(com.sjtu.secondhand.model.Item item) {
<span class="nc" id="L822">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L823">        result.put(&quot;id&quot;, item.getId());</span>
<span class="nc" id="L824">        result.put(&quot;name&quot;, item.getName());</span>
<span class="nc" id="L825">        result.put(&quot;title&quot;, item.getName()); // 为了兼容性</span>
<span class="nc" id="L826">        result.put(&quot;description&quot;, item.getDescription());</span>
<span class="nc" id="L827">        result.put(&quot;price&quot;, item.getPrice());</span>
<span class="nc" id="L828">        result.put(&quot;priceMin&quot;, item.getPriceMin());</span>
<span class="nc" id="L829">        result.put(&quot;priceMax&quot;, item.getPriceMax());</span>
<span class="nc bnc" id="L830" title="All 2 branches missed.">        result.put(&quot;itemType&quot;, item.getItemType() != null ? item.getItemType().name() : null);</span>
<span class="nc bnc" id="L831" title="All 2 branches missed.">        result.put(&quot;condition&quot;, item.getCondition() != null ? item.getCondition().name() : null);</span>
<span class="nc bnc" id="L832" title="All 2 branches missed.">        result.put(&quot;status&quot;, item.getStatus() != null ? item.getStatus().name() : null);</span>
<span class="nc" id="L833">        result.put(&quot;viewCount&quot;, item.getViewCount());</span>
<span class="nc" id="L834">        result.put(&quot;favoriteCount&quot;, item.getFavoriteCount());</span>
<span class="nc" id="L835">        result.put(&quot;createdAt&quot;, item.getCreatedAt());</span>
<span class="nc" id="L836">        result.put(&quot;updatedAt&quot;, item.getUpdatedAt());</span>
        
        // 转换分类
<span class="nc bnc" id="L839" title="All 2 branches missed.">        if (item.getCategory() != null) {</span>
<span class="nc" id="L840">            Map&lt;String, Object&gt; categoryMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L841">            categoryMap.put(&quot;id&quot;, item.getCategory().getId());</span>
<span class="nc" id="L842">            categoryMap.put(&quot;name&quot;, item.getCategory().getName());</span>
<span class="nc" id="L843">            result.put(&quot;category&quot;, categoryMap);</span>
        }
        
        // 转换用户信息
<span class="nc bnc" id="L847" title="All 2 branches missed.">        if (item.getUser() != null) {</span>
<span class="nc" id="L848">            Map&lt;String, Object&gt; userMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L849">            userMap.put(&quot;id&quot;, item.getUser().getId());</span>
<span class="nc" id="L850">            userMap.put(&quot;username&quot;, item.getUser().getUsername());</span>
<span class="nc" id="L851">            userMap.put(&quot;avatarUrl&quot;, item.getUser().getAvatarUrl());</span>
<span class="nc" id="L852">            userMap.put(&quot;rating&quot;, item.getUser().getRating());</span>
<span class="nc" id="L853">            result.put(&quot;user&quot;, userMap);</span>
<span class="nc" id="L854">            result.put(&quot;seller&quot;, userMap); // 为了兼容性</span>
        }
        
        // 转换图片URL
<span class="nc bnc" id="L858" title="All 4 branches missed.">        if (item.getImages() != null &amp;&amp; !item.getImages().isEmpty()) {</span>
<span class="nc" id="L859">            result.put(&quot;images&quot;, item.getImages().stream()</span>
<span class="nc" id="L860">                    .map(img -&gt; img.getUrl())</span>
<span class="nc" id="L861">                    .collect(Collectors.toList()));</span>
        }
        
<span class="nc" id="L864">        return result;</span>
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>