# Created at 2025-07-18T03:57:52.455
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo.

# Created at 2025-07-18T03:57:52.456
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T03:57:52.456
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T03:57:52.458
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T03:57:52.459
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T03:57:52.459
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T03:57:52.462
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T03:57:52.463
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T03:57:52.465
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T03:57:52.465
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T03:57:52.465
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T03:57:52.466
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T03:57:52.466
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T03:57:52.467
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T03:57:52.467
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T03:57:52.467
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T03:57:52.467
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T03:57:52.467
	at java.base/sun.util.cldr.CLDRLocaleProviderAdapter.<init>(CLDRLocaleProviderAdapter.java:75)

# Created at 2025-07-18T03:57:52.467
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)

# Created at 2025-07-18T03:57:52.467
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)

# Created at 2025-07-18T03:57:52.468
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:483)

# Created at 2025-07-18T03:57:52.468
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.forType(LocaleProviderAdapter.java:181)

# Created at 2025-07-18T03:57:52.468
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.findAdapter(LocaleProviderAdapter.java:280)

# Created at 2025-07-18T03:57:52.468
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.getAdapter(LocaleProviderAdapter.java:251)

# Created at 2025-07-18T03:57:52.468
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1105)

# Created at 2025-07-18T03:57:52.469
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T03:57:52.470
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T03:57:52.470
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T03:57:52.472
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T03:57:52.472
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T03:57:52.472
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T03:57:52.472
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T03:57:52.472
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T03:57:52.473
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T03:57:52.474
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T03:57:52.474
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T03:57:52.476
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T03:57:52.476
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T03:57:52.476
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T03:57:52.476
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T03:57:52.476
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo.

# Created at 2025-07-18T03:57:52.476
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T03:57:52.477
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T03:57:52.477
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T03:57:52.477
	... 38 more

# Created at 2025-07-18T03:57:52.477
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T03:57:52.477
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T03:57:52.477
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T03:57:52.477
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T03:57:52.477
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T03:57:52.477
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T03:57:52.478
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T03:57:52.478
	... 39 more

# Created at 2025-07-18T03:57:52.478
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/provider/NonBaseLocaleDataMetaInfo.

# Created at 2025-07-18T03:57:52.478
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T03:57:52.479
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T03:57:52.479
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T03:57:52.479
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T03:57:52.480
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T03:57:52.480
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T03:57:52.480
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T03:57:52.481
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T03:57:52.481
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T03:57:52.481
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T03:57:52.482
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T03:57:52.482
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T03:57:52.482
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T03:57:52.482
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T03:57:52.482
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T03:57:52.482
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T03:57:52.482
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.createSupportedLocaleString(JRELocaleProviderAdapter.java:423)

# Created at 2025-07-18T03:57:52.482
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.createLanguageTagSet(JRELocaleProviderAdapter.java:410)

# Created at 2025-07-18T03:57:52.482
	at java.base/sun.util.locale.provider.FallbackLocaleProviderAdapter.createLanguageTagSet(FallbackLocaleProviderAdapter.java:67)

# Created at 2025-07-18T03:57:52.483
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getLanguageTagSet(JRELocaleProviderAdapter.java:400)

# Created at 2025-07-18T03:57:52.483
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getNumberFormatProvider(JRELocaleProviderAdapter.java:220)

# Created at 2025-07-18T03:57:52.483
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getLocaleServiceProvider(JRELocaleProviderAdapter.java:96)

# Created at 2025-07-18T03:57:52.483
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.findAdapter(LocaleProviderAdapter.java:282)

# Created at 2025-07-18T03:57:52.483
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.getAdapter(LocaleProviderAdapter.java:251)

# Created at 2025-07-18T03:57:52.483
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1105)

# Created at 2025-07-18T03:57:52.483
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T03:57:52.483
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T03:57:52.483
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T03:57:52.483
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T03:57:52.484
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T03:57:52.484
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T03:57:52.484
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T03:57:52.484
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T03:57:52.484
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T03:57:52.485
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T03:57:52.485
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T03:57:52.485
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T03:57:52.485
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T03:57:52.486
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T03:57:52.486
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T03:57:52.486
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/provider/NonBaseLocaleDataMetaInfo.

# Created at 2025-07-18T03:57:52.486
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T03:57:52.487
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T03:57:52.487
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T03:57:52.489
	... 39 more

# Created at 2025-07-18T03:57:52.496
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T03:57:52.496
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T03:57:52.496
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T03:57:52.496
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T03:57:52.496
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T03:57:52.496
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T03:57:52.496
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T03:57:52.496
	... 40 more

# Created at 2025-07-18T03:57:52.497
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/provider/LocaleDataProvider.

# Created at 2025-07-18T03:57:52.497
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T03:57:52.497
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T03:57:52.497
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T03:57:52.497
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T03:57:52.497
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T03:57:52.497
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T03:57:52.497
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T03:57:52.498
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T03:57:52.498
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T03:57:52.498
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T03:57:52.498
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T03:57:52.499
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T03:57:52.499
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T03:57:52.500
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T03:57:52.500
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T03:57:52.500
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T03:57:52.500
	at java.base/sun.util.resources.Bundles.loadBundleFromProviders(Bundles.java:261)

# Created at 2025-07-18T03:57:52.500
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:199)

# Created at 2025-07-18T03:57:52.500
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:158)

# Created at 2025-07-18T03:57:52.500
	at java.base/sun.util.resources.Bundles.loadBundleOf(Bundles.java:143)

# Created at 2025-07-18T03:57:52.500
	at java.base/sun.util.resources.Bundles.of(Bundles.java:104)

# Created at 2025-07-18T03:57:52.500
	at java.base/sun.util.resources.LocaleData.getBundle(LocaleData.java:179)

# Created at 2025-07-18T03:57:52.501
	at java.base/sun.util.resources.LocaleData.getNumberFormatData(LocaleData.java:175)

# Created at 2025-07-18T03:57:52.501
	at java.base/sun.util.locale.provider.LocaleResources.getNumberPatterns(LocaleResources.java:543)

# Created at 2025-07-18T03:57:52.501
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getInstance(NumberFormatProviderImpl.java:184)

# Created at 2025-07-18T03:57:52.501
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getNumberInstance(NumberFormatProviderImpl.java:151)

# Created at 2025-07-18T03:57:52.501
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1121)

# Created at 2025-07-18T03:57:52.501
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1107)

# Created at 2025-07-18T03:57:52.501
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T03:57:52.501
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T03:57:52.501
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T03:57:52.501
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T03:57:52.501
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T03:57:52.502
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T03:57:52.502
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T03:57:52.502
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T03:57:52.502
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T03:57:52.502
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T03:57:52.502
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T03:57:52.502
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T03:57:52.502
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T03:57:52.502
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T03:57:52.502
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T03:57:52.502
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/provider/LocaleDataProvider.

# Created at 2025-07-18T03:57:52.502
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T03:57:52.503
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T03:57:52.503
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T03:57:52.503
	... 42 more

# Created at 2025-07-18T03:57:52.503
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T03:57:52.503
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T03:57:52.503
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T03:57:52.503
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T03:57:52.503
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T03:57:52.503
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T03:57:52.505
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T03:57:52.506
	... 43 more

# Created at 2025-07-18T03:57:52.506
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/text/resources/cldr/ext/FormatData_zh.

# Created at 2025-07-18T03:57:52.506
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T03:57:52.506
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T03:57:52.507
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T03:57:52.507
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T03:57:52.507
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T03:57:52.508
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T03:57:52.508
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T03:57:52.509
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T03:57:52.509
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T03:57:52.509
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T03:57:52.509
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T03:57:52.509
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T03:57:52.509
	at jdk.localedata/sun.util.resources.provider.LocaleDataProvider.loadResourceBundle(LocaleDataProvider.java:53)

# Created at 2025-07-18T03:57:52.510
	at jdk.localedata/sun.util.resources.provider.LocaleDataProvider.getBundle(LocaleDataProvider.java:39)

# Created at 2025-07-18T03:57:52.510
	at java.base/sun.util.resources.Bundles.loadBundleFromProviders(Bundles.java:264)

# Created at 2025-07-18T03:57:52.510
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:199)

# Created at 2025-07-18T03:57:52.510
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:158)

# Created at 2025-07-18T03:57:52.510
	at java.base/sun.util.resources.Bundles.loadBundleOf(Bundles.java:143)

# Created at 2025-07-18T03:57:52.510
	at java.base/sun.util.resources.Bundles.of(Bundles.java:104)

# Created at 2025-07-18T03:57:52.510
	at java.base/sun.util.resources.LocaleData.getBundle(LocaleData.java:179)

# Created at 2025-07-18T03:57:52.510
	at java.base/sun.util.resources.LocaleData.getNumberFormatData(LocaleData.java:175)

# Created at 2025-07-18T03:57:52.510
	at java.base/sun.util.locale.provider.LocaleResources.getNumberPatterns(LocaleResources.java:543)

# Created at 2025-07-18T03:57:52.510
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getInstance(NumberFormatProviderImpl.java:184)

# Created at 2025-07-18T03:57:52.510
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getNumberInstance(NumberFormatProviderImpl.java:151)

# Created at 2025-07-18T03:57:52.510
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1121)

# Created at 2025-07-18T03:57:52.510
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1107)

# Created at 2025-07-18T03:57:52.510
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T03:57:52.511
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T03:57:52.511
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T03:57:52.511
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T03:57:52.511
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T03:57:52.511
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T03:57:52.511
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T03:57:52.511
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T03:57:52.511
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T03:57:52.511
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T03:57:52.512
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T03:57:52.512
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T03:57:52.512
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T03:57:52.512
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T03:57:52.512
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T03:57:52.530
Caused by: java.io.IOException: Error while instrumenting sun/text/resources/cldr/ext/FormatData_zh.

# Created at 2025-07-18T03:57:52.533
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T03:57:52.535
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T03:57:52.537
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T03:57:52.537
	... 40 more

# Created at 2025-07-18T03:57:52.537
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T03:57:52.537
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T03:57:52.538
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T03:57:52.538
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T03:57:52.538
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T03:57:52.538
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T03:57:52.539
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T03:57:52.539
	... 41 more

