# Created at 2025-07-18T04:10:45.921
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo.

# Created at 2025-07-18T04:10:45.924
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T04:10:45.924
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T04:10:45.927
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T04:10:45.927
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T04:10:45.928
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T04:10:45.929
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T04:10:45.929
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T04:10:45.930
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T04:10:45.931
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T04:10:45.932
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T04:10:45.932
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T04:10:45.940
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T04:10:45.943
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T04:10:45.945
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T04:10:45.946
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T04:10:45.947
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T04:10:45.948
	at java.base/sun.util.cldr.CLDRLocaleProviderAdapter.<init>(CLDRLocaleProviderAdapter.java:75)

# Created at 2025-07-18T04:10:45.948
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)

# Created at 2025-07-18T04:10:45.948
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)

# Created at 2025-07-18T04:10:45.948
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:483)

# Created at 2025-07-18T04:10:45.949
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.forType(LocaleProviderAdapter.java:181)

# Created at 2025-07-18T04:10:45.949
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.findAdapter(LocaleProviderAdapter.java:280)

# Created at 2025-07-18T04:10:45.949
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.getAdapter(LocaleProviderAdapter.java:251)

# Created at 2025-07-18T04:10:45.950
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1105)

# Created at 2025-07-18T04:10:45.950
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T04:10:45.951
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T04:10:45.951
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T04:10:45.961
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T04:10:45.961
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T04:10:45.964
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T04:10:45.965
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T04:10:45.965
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T04:10:45.966
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T04:10:45.966
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T04:10:45.966
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T04:10:45.966
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T04:10:45.967
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T04:10:45.967
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T04:10:45.968
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T04:10:45.969
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo.

# Created at 2025-07-18T04:10:45.973
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T04:10:45.973
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T04:10:45.974
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T04:10:45.976
	... 38 more

# Created at 2025-07-18T04:10:45.976
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T04:10:45.977
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T04:10:45.977
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T04:10:45.977
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T04:10:45.978
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T04:10:45.978
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T04:10:45.978
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T04:10:45.978
	... 39 more

# Created at 2025-07-18T04:10:45.978
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/provider/NonBaseLocaleDataMetaInfo.

# Created at 2025-07-18T04:10:45.978
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T04:10:45.979
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T04:10:45.979
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T04:10:45.982
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T04:10:45.982
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T04:10:45.982
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T04:10:45.982
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T04:10:45.983
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T04:10:45.984
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T04:10:45.988
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T04:10:45.989
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T04:10:45.990
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T04:10:45.990
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T04:10:45.990
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T04:10:45.990
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T04:10:45.990
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T04:10:45.991
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.createSupportedLocaleString(JRELocaleProviderAdapter.java:423)

# Created at 2025-07-18T04:10:45.991
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.createLanguageTagSet(JRELocaleProviderAdapter.java:410)

# Created at 2025-07-18T04:10:45.991
	at java.base/sun.util.locale.provider.FallbackLocaleProviderAdapter.createLanguageTagSet(FallbackLocaleProviderAdapter.java:67)

# Created at 2025-07-18T04:10:45.991
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getLanguageTagSet(JRELocaleProviderAdapter.java:400)

# Created at 2025-07-18T04:10:45.992
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getNumberFormatProvider(JRELocaleProviderAdapter.java:220)

# Created at 2025-07-18T04:10:45.992
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getLocaleServiceProvider(JRELocaleProviderAdapter.java:96)

# Created at 2025-07-18T04:10:45.993
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.findAdapter(LocaleProviderAdapter.java:282)

# Created at 2025-07-18T04:10:45.993
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.getAdapter(LocaleProviderAdapter.java:251)

# Created at 2025-07-18T04:10:45.996
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1105)

# Created at 2025-07-18T04:10:45.996
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T04:10:45.997
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T04:10:45.997
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T04:10:45.997
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T04:10:45.998
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T04:10:45.998
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T04:10:45.998
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T04:10:45.998
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T04:10:45.998
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T04:10:45.998
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T04:10:45.998
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T04:10:45.998
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T04:10:45.999
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T04:10:45.999
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T04:10:45.999
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T04:10:45.999
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/provider/NonBaseLocaleDataMetaInfo.

# Created at 2025-07-18T04:10:45.999
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T04:10:45.999
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T04:10:45.999
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T04:10:45.999
	... 39 more

# Created at 2025-07-18T04:10:45.999
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T04:10:45.999
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T04:10:46.000
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T04:10:46.000
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T04:10:46.000
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T04:10:46.000
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T04:10:46.003
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T04:10:46.003
	... 40 more

# Created at 2025-07-18T04:10:46.003
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/provider/LocaleDataProvider.

# Created at 2025-07-18T04:10:46.006
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T04:10:46.006
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T04:10:46.009
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T04:10:46.010
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T04:10:46.010
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T04:10:46.010
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T04:10:46.011
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T04:10:46.012
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T04:10:46.012
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T04:10:46.012
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T04:10:46.013
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T04:10:46.013
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T04:10:46.013
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T04:10:46.014
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T04:10:46.014
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T04:10:46.014
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T04:10:46.014
	at java.base/sun.util.resources.Bundles.loadBundleFromProviders(Bundles.java:261)

# Created at 2025-07-18T04:10:46.015
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:199)

# Created at 2025-07-18T04:10:46.015
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:158)

# Created at 2025-07-18T04:10:46.015
	at java.base/sun.util.resources.Bundles.loadBundleOf(Bundles.java:143)

# Created at 2025-07-18T04:10:46.015
	at java.base/sun.util.resources.Bundles.of(Bundles.java:104)

# Created at 2025-07-18T04:10:46.015
	at java.base/sun.util.resources.LocaleData.getBundle(LocaleData.java:179)

# Created at 2025-07-18T04:10:46.016
	at java.base/sun.util.resources.LocaleData.getNumberFormatData(LocaleData.java:175)

# Created at 2025-07-18T04:10:46.016
	at java.base/sun.util.locale.provider.LocaleResources.getNumberPatterns(LocaleResources.java:543)

# Created at 2025-07-18T04:10:46.019
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getInstance(NumberFormatProviderImpl.java:184)

# Created at 2025-07-18T04:10:46.019
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getNumberInstance(NumberFormatProviderImpl.java:151)

# Created at 2025-07-18T04:10:46.019
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1121)

# Created at 2025-07-18T04:10:46.019
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1107)

# Created at 2025-07-18T04:10:46.019
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T04:10:46.019
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T04:10:46.019
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T04:10:46.019
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T04:10:46.020
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T04:10:46.020
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T04:10:46.020
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T04:10:46.020
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T04:10:46.020
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T04:10:46.021
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T04:10:46.021
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T04:10:46.021
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T04:10:46.021
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T04:10:46.021
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T04:10:46.021
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T04:10:46.021
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/provider/LocaleDataProvider.

# Created at 2025-07-18T04:10:46.021
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T04:10:46.021
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T04:10:46.021
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T04:10:46.022
	... 42 more

# Created at 2025-07-18T04:10:46.022
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T04:10:46.022
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T04:10:46.023
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T04:10:46.023
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T04:10:46.024
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T04:10:46.024
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T04:10:46.024
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T04:10:46.024
	... 43 more

# Created at 2025-07-18T04:10:46.024
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/text/resources/cldr/ext/FormatData_zh.

# Created at 2025-07-18T04:10:46.024
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T04:10:46.024
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T04:10:46.024
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T04:10:46.025
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T04:10:46.025
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T04:10:46.025
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T04:10:46.025
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T04:10:46.025
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T04:10:46.025
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T04:10:46.025
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T04:10:46.025
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T04:10:46.025
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T04:10:46.025
	at jdk.localedata/sun.util.resources.provider.LocaleDataProvider.loadResourceBundle(LocaleDataProvider.java:53)

# Created at 2025-07-18T04:10:46.025
	at jdk.localedata/sun.util.resources.provider.LocaleDataProvider.getBundle(LocaleDataProvider.java:39)

# Created at 2025-07-18T04:10:46.025
	at java.base/sun.util.resources.Bundles.loadBundleFromProviders(Bundles.java:264)

# Created at 2025-07-18T04:10:46.025
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:199)

# Created at 2025-07-18T04:10:46.025
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:158)

# Created at 2025-07-18T04:10:46.025
	at java.base/sun.util.resources.Bundles.loadBundleOf(Bundles.java:143)

# Created at 2025-07-18T04:10:46.026
	at java.base/sun.util.resources.Bundles.of(Bundles.java:104)

# Created at 2025-07-18T04:10:46.026
	at java.base/sun.util.resources.LocaleData.getBundle(LocaleData.java:179)

# Created at 2025-07-18T04:10:46.026
	at java.base/sun.util.resources.LocaleData.getNumberFormatData(LocaleData.java:175)

# Created at 2025-07-18T04:10:46.026
	at java.base/sun.util.locale.provider.LocaleResources.getNumberPatterns(LocaleResources.java:543)

# Created at 2025-07-18T04:10:46.026
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getInstance(NumberFormatProviderImpl.java:184)

# Created at 2025-07-18T04:10:46.026
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getNumberInstance(NumberFormatProviderImpl.java:151)

# Created at 2025-07-18T04:10:46.026
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1121)

# Created at 2025-07-18T04:10:46.026
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1107)

# Created at 2025-07-18T04:10:46.027
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T04:10:46.027
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T04:10:46.027
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T04:10:46.027
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T04:10:46.027
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T04:10:46.027
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T04:10:46.027
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T04:10:46.028
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T04:10:46.028
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T04:10:46.028
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T04:10:46.028
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T04:10:46.028
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T04:10:46.028
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T04:10:46.028
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T04:10:46.028
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T04:10:46.029
Caused by: java.io.IOException: Error while instrumenting sun/text/resources/cldr/ext/FormatData_zh.

# Created at 2025-07-18T04:10:46.029
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T04:10:46.029
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T04:10:46.030
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T04:10:46.030
	... 40 more

# Created at 2025-07-18T04:10:46.030
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T04:10:46.030
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T04:10:46.031
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T04:10:46.031
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T04:10:46.031
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T04:10:46.031
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T04:10:46.031
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T04:10:46.031
	... 41 more

