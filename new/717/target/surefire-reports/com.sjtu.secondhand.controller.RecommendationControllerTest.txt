-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.RecommendationControllerTest
-------------------------------------------------------------------------------
Tests run: 19, Failures: 1, Errors: 6, Skipped: 0, Time elapsed: 1.978 s <<< FAILURE! - in com.sjtu.secondhand.controller.RecommendationControllerTest
getForYouRecommendations_NotAuthenticated  Time elapsed: 0.051 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_NotAuthenticated(RecommendationControllerTest.java:212)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_NotAuthenticated(RecommendationControllerTest.java:212)

getForYouRecommendations_CustomLimit  Time elapsed: 0.033 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_CustomLimit(RecommendationControllerTest.java:231)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_CustomLimit(RecommendationControllerTest.java:231)

triggerSimilarityCalculation_NoAuth  Time elapsed: 0.015 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<403> but was:<200>
	at com.sjtu.secondhand.controller.RecommendationControllerTest.triggerSimilarityCalculation_NoAuth(RecommendationControllerTest.java:289)

getForYouRecommendations_Authenticated  Time elapsed: 0.015 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_Authenticated(RecommendationControllerTest.java:255)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_Authenticated(RecommendationControllerTest.java:255)

getSimilarItems_ServiceException  Time elapsed: 0.018 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 相似度计算异常
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getSimilarItems_ServiceException(RecommendationControllerTest.java:196)
Caused by: java.lang.RuntimeException: 相似度计算异常
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getSimilarItems_ServiceException(RecommendationControllerTest.java:196)

getHotRecommendations_ServiceException  Time elapsed: 0.016 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 推荐服务异常
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getHotRecommendations_ServiceException(RecommendationControllerTest.java:121)
Caused by: java.lang.RuntimeException: 推荐服务异常
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getHotRecommendations_ServiceException(RecommendationControllerTest.java:121)

getForYouRecommendations_AuthenticatedCustomLimit  Time elapsed: 0.016 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_AuthenticatedCustomLimit(RecommendationControllerTest.java:275)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_AuthenticatedCustomLimit(RecommendationControllerTest.java:275)

