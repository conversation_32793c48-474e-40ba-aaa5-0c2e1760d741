# Created at 2025-07-18T04:03:33.276
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo.

# Created at 2025-07-18T04:03:33.277
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T04:03:33.285
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T04:03:33.285
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T04:03:33.286
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T04:03:33.286
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T04:03:33.288
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T04:03:33.290
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T04:03:33.291
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T04:03:33.292
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T04:03:33.292
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T04:03:33.293
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T04:03:33.293
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T04:03:33.294
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T04:03:33.294
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T04:03:33.295
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T04:03:33.295
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T04:03:33.296
	at java.base/sun.util.cldr.CLDRLocaleProviderAdapter.<init>(CLDRLocaleProviderAdapter.java:75)

# Created at 2025-07-18T04:03:33.296
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)

# Created at 2025-07-18T04:03:33.296
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)

# Created at 2025-07-18T04:03:33.297
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:483)

# Created at 2025-07-18T04:03:33.297
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.forType(LocaleProviderAdapter.java:181)

# Created at 2025-07-18T04:03:33.298
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.findAdapter(LocaleProviderAdapter.java:280)

# Created at 2025-07-18T04:03:33.298
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.getAdapter(LocaleProviderAdapter.java:251)

# Created at 2025-07-18T04:03:33.299
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1105)

# Created at 2025-07-18T04:03:33.299
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T04:03:33.299
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T04:03:33.299
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T04:03:33.299
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T04:03:33.300
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T04:03:33.300
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T04:03:33.300
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T04:03:33.300
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T04:03:33.300
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T04:03:33.300
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T04:03:33.300
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T04:03:33.300
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T04:03:33.301
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T04:03:33.301
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T04:03:33.301
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T04:03:33.302
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo.

# Created at 2025-07-18T04:03:33.302
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T04:03:33.302
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T04:03:33.302
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T04:03:33.302
	... 38 more

# Created at 2025-07-18T04:03:33.302
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T04:03:33.303
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T04:03:33.303
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T04:03:33.303
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T04:03:33.304
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T04:03:33.304
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T04:03:33.304
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T04:03:33.304
	... 39 more

# Created at 2025-07-18T04:03:33.304
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/provider/NonBaseLocaleDataMetaInfo.

# Created at 2025-07-18T04:03:33.304
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T04:03:33.304
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T04:03:33.304
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T04:03:33.305
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T04:03:33.305
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T04:03:33.305
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T04:03:33.305
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T04:03:33.305
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T04:03:33.305
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T04:03:33.305
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T04:03:33.305
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T04:03:33.305
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T04:03:33.305
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T04:03:33.306
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T04:03:33.306
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T04:03:33.306
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T04:03:33.306
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.createSupportedLocaleString(JRELocaleProviderAdapter.java:423)

# Created at 2025-07-18T04:03:33.306
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.createLanguageTagSet(JRELocaleProviderAdapter.java:410)

# Created at 2025-07-18T04:03:33.307
	at java.base/sun.util.locale.provider.FallbackLocaleProviderAdapter.createLanguageTagSet(FallbackLocaleProviderAdapter.java:67)

# Created at 2025-07-18T04:03:33.307
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getLanguageTagSet(JRELocaleProviderAdapter.java:400)

# Created at 2025-07-18T04:03:33.307
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getNumberFormatProvider(JRELocaleProviderAdapter.java:220)

# Created at 2025-07-18T04:03:33.307
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getLocaleServiceProvider(JRELocaleProviderAdapter.java:96)

# Created at 2025-07-18T04:03:33.308
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.findAdapter(LocaleProviderAdapter.java:282)

# Created at 2025-07-18T04:03:33.308
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.getAdapter(LocaleProviderAdapter.java:251)

# Created at 2025-07-18T04:03:33.308
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1105)

# Created at 2025-07-18T04:03:33.308
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T04:03:33.309
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T04:03:33.309
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T04:03:33.309
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T04:03:33.309
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T04:03:33.309
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T04:03:33.309
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T04:03:33.310
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T04:03:33.310
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T04:03:33.310
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T04:03:33.311
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T04:03:33.311
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T04:03:33.312
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T04:03:33.312
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T04:03:33.312
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T04:03:33.312
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/provider/NonBaseLocaleDataMetaInfo.

# Created at 2025-07-18T04:03:33.312
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T04:03:33.312
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T04:03:33.313
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T04:03:33.313
	... 39 more

# Created at 2025-07-18T04:03:33.313
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T04:03:33.313
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T04:03:33.314
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T04:03:33.315
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T04:03:33.316
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T04:03:33.316
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T04:03:33.316
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T04:03:33.316
	... 40 more

# Created at 2025-07-18T04:03:33.316
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/provider/LocaleDataProvider.

# Created at 2025-07-18T04:03:33.316
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T04:03:33.316
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T04:03:33.316
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T04:03:33.317
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T04:03:33.317
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T04:03:33.317
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T04:03:33.317
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T04:03:33.317
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T04:03:33.317
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T04:03:33.317
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T04:03:33.317
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T04:03:33.317
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T04:03:33.318
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T04:03:33.318
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T04:03:33.318
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T04:03:33.318
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T04:03:33.318
	at java.base/sun.util.resources.Bundles.loadBundleFromProviders(Bundles.java:261)

# Created at 2025-07-18T04:03:33.318
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:199)

# Created at 2025-07-18T04:03:33.318
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:158)

# Created at 2025-07-18T04:03:33.318
	at java.base/sun.util.resources.Bundles.loadBundleOf(Bundles.java:143)

# Created at 2025-07-18T04:03:33.318
	at java.base/sun.util.resources.Bundles.of(Bundles.java:104)

# Created at 2025-07-18T04:03:33.318
	at java.base/sun.util.resources.LocaleData.getBundle(LocaleData.java:179)

# Created at 2025-07-18T04:03:33.318
	at java.base/sun.util.resources.LocaleData.getNumberFormatData(LocaleData.java:175)

# Created at 2025-07-18T04:03:33.319
	at java.base/sun.util.locale.provider.LocaleResources.getNumberPatterns(LocaleResources.java:543)

# Created at 2025-07-18T04:03:33.319
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getInstance(NumberFormatProviderImpl.java:184)

# Created at 2025-07-18T04:03:33.319
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getNumberInstance(NumberFormatProviderImpl.java:151)

# Created at 2025-07-18T04:03:33.319
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1121)

# Created at 2025-07-18T04:03:33.319
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1107)

# Created at 2025-07-18T04:03:33.319
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T04:03:33.319
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T04:03:33.320
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T04:03:33.320
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T04:03:33.320
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T04:03:33.320
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T04:03:33.320
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T04:03:33.321
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T04:03:33.321
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T04:03:33.322
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T04:03:33.322
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T04:03:33.322
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T04:03:33.322
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T04:03:33.322
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T04:03:33.322
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T04:03:33.322
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/provider/LocaleDataProvider.

# Created at 2025-07-18T04:03:33.323
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T04:03:33.323
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T04:03:33.323
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T04:03:33.323
	... 42 more

# Created at 2025-07-18T04:03:33.324
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T04:03:33.324
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T04:03:33.324
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T04:03:33.324
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T04:03:33.324
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T04:03:33.325
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T04:03:33.325
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T04:03:33.327
	... 43 more

# Created at 2025-07-18T04:03:33.327
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/text/resources/cldr/ext/FormatData_zh.

# Created at 2025-07-18T04:03:33.331
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T04:03:33.331
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T04:03:33.331
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T04:03:33.331
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T04:03:33.331
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T04:03:33.331
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T04:03:33.331
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T04:03:33.331
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T04:03:33.331
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T04:03:33.337
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T04:03:33.337
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T04:03:33.337
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T04:03:33.337
	at jdk.localedata/sun.util.resources.provider.LocaleDataProvider.loadResourceBundle(LocaleDataProvider.java:53)

# Created at 2025-07-18T04:03:33.337
	at jdk.localedata/sun.util.resources.provider.LocaleDataProvider.getBundle(LocaleDataProvider.java:39)

# Created at 2025-07-18T04:03:33.338
	at java.base/sun.util.resources.Bundles.loadBundleFromProviders(Bundles.java:264)

# Created at 2025-07-18T04:03:33.338
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:199)

# Created at 2025-07-18T04:03:33.338
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:158)

# Created at 2025-07-18T04:03:33.338
	at java.base/sun.util.resources.Bundles.loadBundleOf(Bundles.java:143)

# Created at 2025-07-18T04:03:33.338
	at java.base/sun.util.resources.Bundles.of(Bundles.java:104)

# Created at 2025-07-18T04:03:33.338
	at java.base/sun.util.resources.LocaleData.getBundle(LocaleData.java:179)

# Created at 2025-07-18T04:03:33.338
	at java.base/sun.util.resources.LocaleData.getNumberFormatData(LocaleData.java:175)

# Created at 2025-07-18T04:03:33.338
	at java.base/sun.util.locale.provider.LocaleResources.getNumberPatterns(LocaleResources.java:543)

# Created at 2025-07-18T04:03:33.339
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getInstance(NumberFormatProviderImpl.java:184)

# Created at 2025-07-18T04:03:33.339
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getNumberInstance(NumberFormatProviderImpl.java:151)

# Created at 2025-07-18T04:03:33.339
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1121)

# Created at 2025-07-18T04:03:33.339
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1107)

# Created at 2025-07-18T04:03:33.339
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T04:03:33.339
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T04:03:33.339
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T04:03:33.339
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T04:03:33.340
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T04:03:33.340
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T04:03:33.343
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T04:03:33.343
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T04:03:33.343
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T04:03:33.344
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T04:03:33.344
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T04:03:33.344
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T04:03:33.344
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T04:03:33.344
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T04:03:33.344
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T04:03:33.344
Caused by: java.io.IOException: Error while instrumenting sun/text/resources/cldr/ext/FormatData_zh.

# Created at 2025-07-18T04:03:33.344
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T04:03:33.345
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T04:03:33.346
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T04:03:33.347
	... 40 more

# Created at 2025-07-18T04:03:33.348
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T04:03:33.348
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T04:03:33.349
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T04:03:33.349
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T04:03:33.349
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T04:03:33.349
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T04:03:33.349
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T04:03:33.349
	... 41 more

