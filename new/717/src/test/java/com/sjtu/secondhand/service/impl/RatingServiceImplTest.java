package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.RatingRequest;
import com.sjtu.secondhand.dto.response.RatingResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.*;
import com.sjtu.secondhand.repository.OrderRepository;
import com.sjtu.secondhand.repository.RatingRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RatingServiceImplTest {

    @Mock
    private RatingRepository ratingRepository;

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private UserService userService;

    @InjectMocks
    private RatingServiceImpl ratingService;

    private User currentUser;
    private User otherUser;
    private Order order;
    private Rating rating;
    private RatingRequest ratingRequest;

    @BeforeEach
    void setUp() {
        currentUser = new User();
        currentUser.setId(1L);
        currentUser.setUsername("testuser");
        currentUser.setCreditScore(80);

        otherUser = new User();
        otherUser.setId(2L);
        otherUser.setUsername("otheruser");
        otherUser.setCreditScore(75);

        order = new Order();
        order.setId(1L);
        order.setBuyer(currentUser);
        order.setSeller(otherUser);
        order.setStatus(Order.OrderStatus.COMPLETED);

        rating = new Rating();
        rating.setId(1L);
        rating.setRater(currentUser);
        rating.setRatee(otherUser);
        rating.setScore((byte) 5);
        rating.setTransactionType(Rating.TransactionType.IDLE);
        rating.setRelatedTransactionId(1L);

        ratingRequest = new RatingRequest();
        ratingRequest.setScore(5);
        ratingRequest.setTransaction_type("IDLE");
        ratingRequest.setRelated_transaction_id(1);
    }

    @Test
    void createRating_Success() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, currentUser)).thenReturn(false);
        when(ratingRepository.save(any(Rating.class))).thenReturn(rating);
        when(userRepository.findById(2L)).thenReturn(Optional.of(otherUser));
        when(userRepository.save(any(User.class))).thenReturn(otherUser);

        // Act
        RatingResponse response = ratingService.createRating(ratingRequest);

        // Assert
        assertNotNull(response);
        verify(ratingRepository).save(any(Rating.class));
        verify(userRepository).save(any(User.class));
    }

    @Test
    void createRating_OrderNotFound() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(orderRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> ratingService.createRating(ratingRequest));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("订单不存在", exception.getMessage());
    }

         @Test
     void createRating_OrderNotCompleted() {
         // Arrange
         order.setStatus(Order.OrderStatus.CONFIRMED);
         when(userService.getCurrentUser()).thenReturn(currentUser);
         when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

         // Act & Assert
         ApiException exception = assertThrows(ApiException.class, 
             () -> ratingService.createRating(ratingRequest));
         assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
         assertEquals("只能评价已完成的订单", exception.getMessage());
     }

    @Test
    void createRating_AlreadyRated() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, currentUser)).thenReturn(true);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> ratingService.createRating(ratingRequest));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("您已经对该交易进行了评价", exception.getMessage());
    }

    @Test
    void createRating_InvalidTransactionType() {
        // Arrange
        ratingRequest.setTransaction_type("INVALID");
        when(userService.getCurrentUser()).thenReturn(currentUser);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> ratingService.createRating(ratingRequest));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("无效的交易类型", exception.getMessage());
    }

    @Test
    void createRating_UserNotInvolvedInOrder() {
        // Arrange
        User uninvolvedUser = new User();
        uninvolvedUser.setId(3L);
        uninvolvedUser.setUsername("uninvolved");
        
        when(userService.getCurrentUser()).thenReturn(uninvolvedUser);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, uninvolvedUser)).thenReturn(false);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> ratingService.createRating(ratingRequest));
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertEquals("您不能评价此订单", exception.getMessage());
    }

    @Test
    void getRatingById_Success() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(ratingRepository.findById(1L)).thenReturn(Optional.of(rating));

        // Act
        RatingResponse response = ratingService.getRatingById(1L);

        // Assert
        assertNotNull(response);
        verify(ratingRepository).findById(1L);
    }

    @Test
    void getRatingById_NotFound() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(ratingRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> ratingService.getRatingById(1L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("评价不存在", exception.getMessage());
    }

    @Test
    void getMyRatings_Success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Rating> ratingsPage = new PageImpl<>(Arrays.asList(rating));
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(ratingRepository.findByRater(currentUser, pageable)).thenReturn(ratingsPage);

        // Act
        Page<RatingResponse> response = ratingService.getMyRatings(pageable);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        verify(ratingRepository).findByRater(currentUser, pageable);
    }

    @Test
    void getRatingsAboutMe_Success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Rating> ratingsPage = new PageImpl<>(Arrays.asList(rating));
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(ratingRepository.findByRatee(currentUser, pageable)).thenReturn(ratingsPage);

        // Act
        Page<RatingResponse> response = ratingService.getRatingsAboutMe(pageable);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        verify(ratingRepository).findByRatee(currentUser, pageable);
    }

    @Test
    void getUserRatings_Success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Rating> ratingsPage = new PageImpl<>(Arrays.asList(rating));
        when(userService.getUserById(2L)).thenReturn(otherUser);
        when(ratingRepository.findByRatee(otherUser, pageable)).thenReturn(ratingsPage);

        // Act
        Page<RatingResponse> response = ratingService.getUserRatings(2L, pageable);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        verify(ratingRepository).findByRatee(otherUser, pageable);
    }

    @Test
    void getMyRatingsByTransactionType_Success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Rating> ratingsPage = new PageImpl<>(Arrays.asList(rating));
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(ratingRepository.findByRaterAndTransactionType(currentUser, Rating.TransactionType.IDLE, pageable))
            .thenReturn(ratingsPage);

        // Act
        Page<RatingResponse> response = ratingService.getMyRatingsByTransactionType("IDLE", pageable);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        verify(ratingRepository).findByRaterAndTransactionType(currentUser, Rating.TransactionType.IDLE, pageable);
    }

    @Test
    void getRatingsAboutMeByTransactionType_Success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Rating> ratingsPage = new PageImpl<>(Arrays.asList(rating));
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(ratingRepository.findByRateeAndTransactionType(currentUser, Rating.TransactionType.IDLE, pageable))
            .thenReturn(ratingsPage);

        // Act
        Page<RatingResponse> response = ratingService.getRatingsAboutMeByTransactionType("IDLE", pageable);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        verify(ratingRepository).findByRateeAndTransactionType(currentUser, Rating.TransactionType.IDLE, pageable);
    }

    @Test
    void getPendingOrderRatings_Success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Order> ordersPage = new PageImpl<>(Arrays.asList(order));
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(orderRepository.findByBuyerAndStatusAndIsBuyerRatedFalse(currentUser, Order.OrderStatus.COMPLETED, pageable))
            .thenReturn(ordersPage);

        // Act
        Page<RatingResponse> response = ratingService.getPendingOrderRatings(pageable);

        // Assert
        assertNotNull(response);
        verify(orderRepository).findByBuyerAndStatusAndIsBuyerRatedFalse(currentUser, Order.OrderStatus.COMPLETED, pageable);
    }

    @Test
    void hasRatedTransaction_True() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, currentUser)).thenReturn(true);

        // Act
        boolean result = ratingService.hasRatedTransaction(1L);

        // Assert
        assertTrue(result);
        verify(ratingRepository).existsByRelatedTransactionIdAndRater(1L, currentUser);
    }

    @Test
    void hasRatedTransaction_False() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, currentUser)).thenReturn(false);

        // Act
        boolean result = ratingService.hasRatedTransaction(1L);

        // Assert
        assertFalse(result);
        verify(ratingRepository).existsByRelatedTransactionIdAndRater(1L, currentUser);
    }

    @Test
    void createRating_WantedTransactionType() {
        // Arrange
        ratingRequest.setTransaction_type("WANTED");
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, currentUser)).thenReturn(false);
        when(ratingRepository.save(any(Rating.class))).thenReturn(rating);
        when(userRepository.findById(2L)).thenReturn(Optional.of(otherUser));
        when(userRepository.save(any(User.class))).thenReturn(otherUser);

        // Act
        RatingResponse response = ratingService.createRating(ratingRequest);

        // Assert
        assertNotNull(response);
        verify(ratingRepository).save(any(Rating.class));
    }

    @Test
    void createRating_DifferentScores() {
        // Test different rating scores and their impact on credit score
        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, currentUser)).thenReturn(false);
        when(ratingRepository.save(any(Rating.class))).thenReturn(rating);
        when(userRepository.findById(2L)).thenReturn(Optional.of(otherUser));
        when(userRepository.save(any(User.class))).thenReturn(otherUser);

        // Test with score 1
        ratingRequest.setScore(1);
        RatingResponse response = ratingService.createRating(ratingRequest);
        assertNotNull(response);

        // Test with score 3  
        ratingRequest.setScore(3);
        response = ratingService.createRating(ratingRequest);
        assertNotNull(response);

        // Test with score 4
        ratingRequest.setScore(4);
        response = ratingService.createRating(ratingRequest);
        assertNotNull(response);

        verify(ratingRepository, times(3)).save(any(Rating.class));
    }
} 