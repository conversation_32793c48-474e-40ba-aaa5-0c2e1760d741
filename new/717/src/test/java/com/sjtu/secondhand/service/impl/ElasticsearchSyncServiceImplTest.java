package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.model.Category;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.model.es.ItemDocument;
import com.sjtu.secondhand.repository.CategoryRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.es.ItemDocumentRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ElasticsearchSyncServiceImplTest {

    @Mock
    private ItemRepository itemRepository;

    @Mock
    private ItemDocumentRepository itemDocumentRepository;

    @Mock
    private CategoryRepository categoryRepository;

    @Mock
    private ElasticsearchOperations elasticsearchOperations;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private ElasticsearchSyncServiceImpl elasticsearchSyncService;

    private Item testItem;
    private User testUser;
    private Category testCategory;
    private ItemDocument testItemDocument;

    @BeforeEach
    void setUp() {
        // 设置测试用的Elasticsearch URI
        ReflectionTestUtils.setField(elasticsearchSyncService, "elasticsearchUri", "http://localhost:9200");
        
        // 使用反射设置RestTemplate
        ReflectionTestUtils.setField(elasticsearchSyncService, "restTemplate", restTemplate);

        // 创建测试数据
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setAvatarUrl("test-avatar.jpg");
        testUser.setCreditScore(45); // 信用分用于计算评分

        testCategory = new Category();
        testCategory.setId(1);
        testCategory.setName("电子产品");

        testItem = new Item();
        testItem.setId(1L);
        testItem.setName("测试商品");
        testItem.setDescription("测试商品描述");
        testItem.setPrice(BigDecimal.valueOf(100.0));
        testItem.setUser(testUser);
        testItem.setCategory(testCategory);
        testItem.setStatus(Item.ItemStatus.FOR_SALE);
        testItem.setViewCount(10);
        testItem.setFavoriteCount(5);
        testItem.setCreatedAt(LocalDateTime.now());
        testItem.setUpdatedAt(LocalDateTime.now());

        testItemDocument = new ItemDocument();
        testItemDocument.setId(1L);
        testItemDocument.setName("测试商品");
        testItemDocument.setDescription("测试商品描述");
        testItemDocument.setPrice(BigDecimal.valueOf(100.0));
    }

    @Test
    void testSyncItemToElasticsearch_Success() {
        // given
        when(itemDocumentRepository.save(any(ItemDocument.class))).thenReturn(testItemDocument);

        // when
        elasticsearchSyncService.syncItemToElasticsearch(testItem);

        // then
        verify(itemDocumentRepository, times(1)).save(any(ItemDocument.class));
    }

    @Test
    void testSyncItemToElasticsearch_NullItem() {
        // when
        elasticsearchSyncService.syncItemToElasticsearch(null);

        // then
        verify(itemDocumentRepository, never()).save(any(ItemDocument.class));
    }

    @Test
    void testSyncItemToElasticsearch_Exception() {
        // given
        when(itemDocumentRepository.save(any(ItemDocument.class)))
                .thenThrow(new RuntimeException("同步失败"));

        // when
        assertDoesNotThrow(() -> elasticsearchSyncService.syncItemToElasticsearch(testItem));

        // then
        verify(itemDocumentRepository, times(1)).save(any(ItemDocument.class));
    }

    @Test
    void testDeleteItemFromElasticsearch_Success() {
        // given
        Long itemId = 1L;
        doNothing().when(itemDocumentRepository).deleteById(itemId);

        // when
        elasticsearchSyncService.deleteItemFromElasticsearch(itemId);

        // then
        verify(itemDocumentRepository, times(1)).deleteById(itemId);
    }

    @Test
    void testDeleteItemFromElasticsearch_Exception() {
        // given
        Long itemId = 1L;
        doThrow(new RuntimeException("删除失败")).when(itemDocumentRepository).deleteById(itemId);

        // when
        assertDoesNotThrow(() -> elasticsearchSyncService.deleteItemFromElasticsearch(itemId));

        // then
        verify(itemDocumentRepository, times(1)).deleteById(itemId);
    }

    @Test
    void testSyncAllItemsToElasticsearch_Success() {
        // given
        List<Item> items = Arrays.asList(testItem);
        when(itemRepository.findAll()).thenReturn(items);
        doNothing().when(itemDocumentRepository).deleteAll();
        when(itemDocumentRepository.saveAll(anyList())).thenReturn(Arrays.asList(testItemDocument));

        // when
        elasticsearchSyncService.syncAllItemsToElasticsearch();

        // then
        verify(itemRepository, times(1)).findAll();
        verify(itemDocumentRepository, times(1)).deleteAll();
        verify(itemDocumentRepository, times(1)).saveAll(anyList());
    }

    @Test
    void testSyncAllItemsToElasticsearch_EmptyItems() {
        // given
        when(itemRepository.findAll()).thenReturn(new ArrayList<>());

        // when
        elasticsearchSyncService.syncAllItemsToElasticsearch();

        // then
        verify(itemRepository, times(1)).findAll();
        verify(itemDocumentRepository, never()).deleteAll();
        verify(itemDocumentRepository, never()).saveAll(anyList());
    }

    @Test
    void testSyncAllItemsToElasticsearch_Exception() {
        // given
        when(itemRepository.findAll()).thenThrow(new RuntimeException("数据库错误"));

        // when & then
        assertThrows(RuntimeException.class, () -> elasticsearchSyncService.syncAllItemsToElasticsearch());
    }

    @Test
    void testSearchItemsByKeyword_ReturnsErrorResponse() {
        // when
        Object result = elasticsearchSyncService.searchItemsByKeyword("测试", 0, 10);

        // then
        assertNotNull(result);
        assertInstanceOf(Map.class, result);
        @SuppressWarnings("unchecked")
        Map<String, Object> response = (Map<String, Object>) result;
        assertEquals("elasticsearch_fallback", response.get("source"));
        assertEquals(0, response.get("total"));
        assertEquals(0, response.get("page"));
        assertEquals(10, response.get("size"));
    }

    @Test
    void testAdvancedSearch_ReturnsErrorResponse() {
        // when
        Object result = elasticsearchSyncService.advancedSearch(
                "测试", 1L, 50.0, 200.0, "IDLE", "BRAND_NEW", 0, 10, "relevance"
        );

        // then
        assertNotNull(result);
        assertInstanceOf(Map.class, result);
        @SuppressWarnings("unchecked")
        Map<String, Object> response = (Map<String, Object>) result;
        assertEquals("elasticsearch_fallback", response.get("source"));
        assertEquals(0, response.get("total"));
        assertEquals(0, response.get("page"));
        assertEquals(10, response.get("size"));
    }

    @Test
    void testDirectSearchByKeyword_Success() {
        // given
        Map<String, Object> esResponse = createMockElasticsearchResponse();
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(esResponse, HttpStatus.OK);
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(responseEntity);

        // when
        Object result = elasticsearchSyncService.directSearchByKeyword("测试", 0, 10);

        // then
        assertNotNull(result);
        assertInstanceOf(Map.class, result);
        @SuppressWarnings("unchecked")
        Map<String, Object> response = (Map<String, Object>) result;
        assertEquals("elasticsearch_direct", response.get("source"));
        assertTrue(response.containsKey("items"));
        verify(restTemplate, times(1)).postForEntity(anyString(), any(HttpEntity.class), eq(Map.class));
    }

    @Test
    void testDirectSearchByKeyword_RestClientException() {
        // given
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenThrow(new RestClientException("连接错误"));

        // when
        Object result = elasticsearchSyncService.directSearchByKeyword("测试", 0, 10);

        // then
        assertNotNull(result);
        assertInstanceOf(Map.class, result);
        @SuppressWarnings("unchecked")
        Map<String, Object> response = (Map<String, Object>) result;
        assertEquals("elasticsearch_error", response.get("source"));
        assertTrue(response.get("error").toString().contains("搜索处理过程中出错"));
    }

    @Test
    void testDirectAdvancedSearch_Success() {
        // given
        Map<String, Object> esResponse = createMockElasticsearchResponse();
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(esResponse, HttpStatus.OK);
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(responseEntity);

        // when
        Object result = elasticsearchSyncService.directAdvancedSearch(
                "测试", 1L, 50.0, 200.0, "IDLE", "BRAND_NEW", 0, 10, "price_asc"
        );

        // then
        assertNotNull(result);
        assertInstanceOf(Map.class, result);
        @SuppressWarnings("unchecked")
        Map<String, Object> response = (Map<String, Object>) result;
        assertEquals("elasticsearch_direct", response.get("source"));
        assertTrue(response.containsKey("items"));
        verify(restTemplate, times(1)).postForEntity(anyString(), any(HttpEntity.class), eq(Map.class));
    }

    @Test
    void testDirectAdvancedSearch_WithDifferentSortOptions() {
        // given
        Map<String, Object> esResponse = createMockElasticsearchResponse();
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(esResponse, HttpStatus.OK);
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(responseEntity);

        // Test different sort options
        String[] sortOptions = {"price_desc", "date_desc", "date_asc", "relevance", null, ""};
        
        for (String sort : sortOptions) {
            // when
            Object result = elasticsearchSyncService.directAdvancedSearch(
                    "测试", null, null, null, null, null, 0, 10, sort
            );

            // then
            assertNotNull(result);
            assertInstanceOf(Map.class, result);
        }

        verify(restTemplate, times(sortOptions.length)).postForEntity(anyString(), any(HttpEntity.class), eq(Map.class));
    }

    @Test
    void testDirectAdvancedSearch_WithNullParameters() {
        // given
        Map<String, Object> esResponse = createMockElasticsearchResponse();
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(esResponse, HttpStatus.OK);
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(responseEntity);

        // when
        Object result = elasticsearchSyncService.directAdvancedSearch(
                null, null, null, null, null, null, 0, 10, null
        );

        // then
        assertNotNull(result);
        assertInstanceOf(Map.class, result);
        @SuppressWarnings("unchecked")
        Map<String, Object> response = (Map<String, Object>) result;
        assertEquals("elasticsearch_direct", response.get("source"));
    }

    @Test
    void testDirectAdvancedSearch_Exception() {
        // given
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenThrow(new RuntimeException("网络错误"));

        // when
        Object result = elasticsearchSyncService.directAdvancedSearch(
                "测试", 1L, 50.0, 200.0, "IDLE", "BRAND_NEW", 0, 10, "relevance"
        );

        // then
        assertNotNull(result);
        assertInstanceOf(Map.class, result);
        @SuppressWarnings("unchecked")
        Map<String, Object> response = (Map<String, Object>) result;
        assertEquals("elasticsearch_error", response.get("source"));
        assertTrue(response.get("error").toString().contains("高级搜索处理过程中出错"));
    }

    @Test
    void testTransformElasticsearchResponse_NullResponse() {
        // 使用反射调用私有方法
        Object result = ReflectionTestUtils.invokeMethod(
                elasticsearchSyncService, "transformElasticsearchResponse", null, 0, 10
        );

        assertNotNull(result);
        assertInstanceOf(Map.class, result);
        @SuppressWarnings("unchecked")
        Map<String, Object> response = (Map<String, Object>) result;
        assertEquals(0, response.get("total"));
        assertTrue(response.get("error").toString().contains("ES返回空响应"));
    }

    @Test
    void testTransformElasticsearchResponse_WithHighlight() {
        // given
        Map<String, Object> esResponse = createMockElasticsearchResponseWithHighlight();

        // when
        Object result = ReflectionTestUtils.invokeMethod(
                elasticsearchSyncService, "transformElasticsearchResponse", esResponse, 0, 10
        );

        // then
        assertNotNull(result);
        assertInstanceOf(Map.class, result);
        @SuppressWarnings("unchecked")
        Map<String, Object> response = (Map<String, Object>) result;
        assertEquals("elasticsearch_direct", response.get("source"));
        assertEquals(1, response.get("total"));
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> items = (List<Map<String, Object>>) response.get("items");
        assertEquals(1, items.size());
    }

    private Map<String, Object> createMockElasticsearchResponse() {
        Map<String, Object> response = new HashMap<>();
        response.put("took", 5);
        response.put("timed_out", false);

        Map<String, Object> shards = new HashMap<>();
        shards.put("total", 5);
        shards.put("successful", 5);
        shards.put("failed", 0);
        response.put("_shards", shards);

        Map<String, Object> total = new HashMap<>();
        total.put("value", 1);
        total.put("relation", "eq");

        Map<String, Object> source = new HashMap<>();
        source.put("id", 1L);
        source.put("name", "测试商品");
        source.put("description", "测试商品描述");
        source.put("price", 100.0);
        source.put("imageUrls", Arrays.asList("image1.jpg", "image2.jpg"));
        
        Map<String, Object> user = new HashMap<>();
        user.put("id", 1L);
        user.put("username", "testuser");
        user.put("avatarUrl", "avatar.jpg");
        source.put("user", user);

        Map<String, Object> category = new HashMap<>();
        category.put("id", 1);
        category.put("name", "电子产品");
        source.put("category", category);

        Map<String, Object> hit = new HashMap<>();
        hit.put("_id", "1");
        hit.put("_score", 1.0);
        hit.put("_source", source);

        Map<String, Object> hits = new HashMap<>();
        hits.put("total", total);
        hits.put("hits", Arrays.asList(hit));

        response.put("hits", hits);
        return response;
    }

    private Map<String, Object> createMockElasticsearchResponseWithHighlight() {
        Map<String, Object> response = createMockElasticsearchResponse();
        
        @SuppressWarnings("unchecked")
        Map<String, Object> hits = (Map<String, Object>) response.get("hits");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> hitsList = (List<Map<String, Object>>) hits.get("hits");
        
        Map<String, Object> hit = hitsList.get(0);
        
        Map<String, Object> highlight = new HashMap<>();
        highlight.put("name", Arrays.asList("<em>测试</em>商品"));
        highlight.put("description", Arrays.asList("<em>测试</em>商品描述"));
        hit.put("highlight", highlight);

        return response;
    }
} 