package com.sjtu.secondhand.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.dto.request.OrderRequest;
import com.sjtu.secondhand.dto.response.OrderResponse;
import com.sjtu.secondhand.service.OrderService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class OrderControllerTest {

    private MockMvc mockMvc;

    @Mock
    private OrderService orderService;

    @InjectMocks
    private OrderController orderController;

    private ObjectMapper objectMapper;
    private OrderRequest testOrderRequest;
    private OrderResponse testOrderResponse;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(orderController).build();
        objectMapper = new ObjectMapper();

        // 设置测试OrderRequest
        testOrderRequest = new OrderRequest();
        testOrderRequest.setItem_id(1);

        // 设置测试OrderResponse
        testOrderResponse = new OrderResponse();
        testOrderResponse.setId(1L);
        testOrderResponse.setStatus("PENDING_CONFIRMATION");
    }

    @Test
    void createOrder_shouldCreateOrderSuccessfully() throws Exception {
        // Arrange
        when(orderService.createOrder(any(OrderRequest.class))).thenReturn(testOrderResponse);

        // Act & Assert
        mockMvc.perform(post("/orders")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testOrderRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("订单创建成功"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.status").value("PENDING_CONFIRMATION"));

        verify(orderService).createOrder(any(OrderRequest.class));
    }

    @Test
    void createOrder_withException_shouldReturnError() throws Exception {
        // Arrange
        when(orderService.createOrder(any(OrderRequest.class)))
                .thenThrow(new RuntimeException("Order creation failed"));

        // Act & Assert
        mockMvc.perform(post("/orders")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testOrderRequest)))
                .andExpect(status().isInternalServerError());

        verify(orderService).createOrder(any(OrderRequest.class));
    }

    @Test
    void getOrderById_shouldReturnOrderDetails() throws Exception {
        // Arrange
        when(orderService.getOrderById(1L)).thenReturn(testOrderResponse);

        // Act & Assert
        mockMvc.perform(get("/orders/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取订单成功"))
                .andExpect(jsonPath("$.data.id").value(1));

        verify(orderService).getOrderById(1L);
    }

    @Test
    void getOrderById_withException_shouldReturnError() throws Exception {
        // Arrange
        when(orderService.getOrderById(1L)).thenThrow(new RuntimeException("Order not found"));

        // Act & Assert
        mockMvc.perform(get("/orders/1"))
                .andExpect(status().isInternalServerError());

        verify(orderService).getOrderById(1L);
    }

    @Test
    void getMyBoughtOrders_shouldReturnOrdersList() throws Exception {
        // Arrange
        Page<OrderResponse> orders = new PageImpl<>(Arrays.asList(testOrderResponse));
        when(orderService.getMyBoughtOrders(any(Pageable.class))).thenReturn(orders);

        // Act & Assert
        mockMvc.perform(get("/orders/my/bought")
                .param("page", "1")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取购买订单列表成功"))
                .andExpect(jsonPath("$.data.content").isArray());

        verify(orderService).getMyBoughtOrders(any(Pageable.class));
    }

    @Test
    void getMyBoughtOrders_withEmptyResult_shouldReturnEmptyList() throws Exception {
        // Arrange
        Page<OrderResponse> emptyOrders = new PageImpl<>(Collections.emptyList());
        when(orderService.getMyBoughtOrders(any(Pageable.class))).thenReturn(emptyOrders);

        // Act & Assert
        mockMvc.perform(get("/orders/my/bought"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content").isEmpty());

        verify(orderService).getMyBoughtOrders(any(Pageable.class));
    }

    @Test
    void getMyBoughtOrders_withException_shouldReturnError() throws Exception {
        // Arrange
        when(orderService.getMyBoughtOrders(any(Pageable.class)))
                .thenThrow(new RuntimeException("Service error"));

        // Act & Assert
        mockMvc.perform(get("/orders/my/bought"))
                .andExpect(status().isInternalServerError());

        verify(orderService).getMyBoughtOrders(any(Pageable.class));
    }

    @Test
    void getMySoldOrders_shouldReturnOrdersList() throws Exception {
        // Arrange
        Page<OrderResponse> orders = new PageImpl<>(Arrays.asList(testOrderResponse));
        when(orderService.getMySoldOrders(any(Pageable.class))).thenReturn(orders);

        // Act & Assert
        mockMvc.perform(get("/orders/my/sold")
                .param("page", "1")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取出售订单列表成功"))
                .andExpect(jsonPath("$.data.content").isArray());

        verify(orderService).getMySoldOrders(any(Pageable.class));
    }

    @Test
    void getMySoldOrders_withEmptyResult_shouldReturnEmptyList() throws Exception {
        // Arrange
        Page<OrderResponse> emptyOrders = new PageImpl<>(Collections.emptyList());
        when(orderService.getMySoldOrders(any(Pageable.class))).thenReturn(emptyOrders);

        // Act & Assert
        mockMvc.perform(get("/orders/my/sold"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content").isEmpty());

        verify(orderService).getMySoldOrders(any(Pageable.class));
    }

    @Test
    void confirmOrder_shouldConfirmOrderSuccessfully() throws Exception {
        // Arrange
        OrderResponse confirmedOrder = new OrderResponse();
        confirmedOrder.setId(1L);
        confirmedOrder.setStatus("AWAITING_ACKNOWLEDGEMENT");
        when(orderService.confirmOrder(1L)).thenReturn(confirmedOrder);

        // Act & Assert
        mockMvc.perform(post("/orders/1/confirm"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("订单确认成功"))
                .andExpect(jsonPath("$.data.status").value("AWAITING_ACKNOWLEDGEMENT"));

        verify(orderService).confirmOrder(1L);
    }

    @Test
    void confirmOrder_withException_shouldReturnError() throws Exception {
        // Arrange
        when(orderService.confirmOrder(1L)).thenThrow(new RuntimeException("Cannot confirm order"));

        // Act & Assert
        mockMvc.perform(post("/orders/1/confirm"))
                .andExpect(status().isInternalServerError());

        verify(orderService).confirmOrder(1L);
    }

    @Test
    void cancelOrder_shouldCancelOrderSuccessfully() throws Exception {
        // Arrange
        OrderResponse cancelledOrder = new OrderResponse();
        cancelledOrder.setId(1L);
        cancelledOrder.setStatus("CANCELLED");
        when(orderService.cancelOrder(1L)).thenReturn(cancelledOrder);

        // Act & Assert
        mockMvc.perform(post("/orders/1/cancel"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("订单取消成功"))
                .andExpect(jsonPath("$.data.status").value("CANCELLED"));

        verify(orderService).cancelOrder(1L);
    }

    @Test
    void cancelOrder_withException_shouldReturnError() throws Exception {
        // Arrange
        when(orderService.cancelOrder(1L)).thenThrow(new RuntimeException("Cannot cancel order"));

        // Act & Assert
        mockMvc.perform(post("/orders/1/cancel"))
                .andExpect(status().isInternalServerError());

        verify(orderService).cancelOrder(1L);
    }

    @Test
    void acknowledgeOrder_shouldAcknowledgeOrderSuccessfully() throws Exception {
        // Arrange
        OrderResponse acknowledgedOrder = new OrderResponse();
        acknowledgedOrder.setId(1L);
        acknowledgedOrder.setStatus("CONFIRMED");
        when(orderService.confirmContact(1L)).thenReturn(acknowledgedOrder);

        // Act & Assert
        mockMvc.perform(post("/orders/1/acknowledge"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("订单确认联系成功"))
                .andExpect(jsonPath("$.data.status").value("CONFIRMED"));

        verify(orderService).confirmContact(1L);
    }

    @Test
    void acknowledgeOrder_withException_shouldReturnError() throws Exception {
        // Arrange
        when(orderService.confirmContact(1L)).thenThrow(new RuntimeException("Cannot acknowledge order"));

        // Act & Assert
        mockMvc.perform(post("/orders/1/acknowledge"))
                .andExpect(status().isInternalServerError());

        verify(orderService).confirmContact(1L);
    }

    @Test
    void getMyBoughtOrders_withDefaultPagination_shouldReturnOrders() throws Exception {
        // Arrange
        Page<OrderResponse> orders = new PageImpl<>(Arrays.asList(testOrderResponse));
        when(orderService.getMyBoughtOrders(any(Pageable.class))).thenReturn(orders);

        // Act & Assert
        mockMvc.perform(get("/orders/my/bought"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(orderService).getMyBoughtOrders(any(Pageable.class));
    }

    @Test
    void getMySoldOrders_withDefaultPagination_shouldReturnOrders() throws Exception {
        // Arrange
        Page<OrderResponse> orders = new PageImpl<>(Arrays.asList(testOrderResponse));
        when(orderService.getMySoldOrders(any(Pageable.class))).thenReturn(orders);

        // Act & Assert
        mockMvc.perform(get("/orders/my/sold"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(orderService).getMySoldOrders(any(Pageable.class));
    }

    @Test
    void getMyBoughtOrders_withZeroPage_shouldUsePageZero() throws Exception {
        // Arrange
        Page<OrderResponse> orders = new PageImpl<>(Arrays.asList(testOrderResponse));
        when(orderService.getMyBoughtOrders(any(Pageable.class))).thenReturn(orders);

        // Act & Assert
        mockMvc.perform(get("/orders/my/bought")
                .param("page", "0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(orderService).getMyBoughtOrders(any(Pageable.class));
    }

    @Test
    void getMySoldOrders_withZeroPage_shouldUsePageZero() throws Exception {
        // Arrange
        Page<OrderResponse> orders = new PageImpl<>(Arrays.asList(testOrderResponse));
        when(orderService.getMySoldOrders(any(Pageable.class))).thenReturn(orders);

        // Act & Assert
        mockMvc.perform(get("/orders/my/sold")
                .param("page", "0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(orderService).getMySoldOrders(any(Pageable.class));
    }
} 