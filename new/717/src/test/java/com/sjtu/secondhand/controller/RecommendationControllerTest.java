package com.sjtu.secondhand.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.dto.response.ItemResponse;

import com.sjtu.secondhand.service.RecommendationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class RecommendationControllerTest {

    @Mock
    private RecommendationService recommendationService;

    @InjectMocks
    private RecommendationController recommendationController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(recommendationController).build();
        objectMapper = new ObjectMapper();
    }

    // Test getHotRecommendations - Success with default limit
    @Test
    void getHotRecommendations_Success() throws Exception {
        List<ItemResponse> mockRecommendations = Arrays.asList(
            createMockItemResponse(1L, "热门商品1", new BigDecimal("100.00")),
            createMockItemResponse(2L, "热门商品2", new BigDecimal("200.00"))
        );

        when(recommendationService.getHotRecommendations(16))
            .thenReturn(mockRecommendations);

        mockMvc.perform(get("/recommendations/hot"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].title").value("热门商品1"))
                .andExpect(jsonPath("$.data[1].title").value("热门商品2"));

        verify(recommendationService).getHotRecommendations(16);
    }

    // Test getHotRecommendations - Custom limit
    @Test
    void getHotRecommendations_CustomLimit() throws Exception {
        List<ItemResponse> mockRecommendations = Arrays.asList(
            createMockItemResponse(1L, "热门商品", new BigDecimal("100.00"))
        );

        when(recommendationService.getHotRecommendations(10))
            .thenReturn(mockRecommendations);

        mockMvc.perform(get("/recommendations/hot")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1));

        verify(recommendationService).getHotRecommendations(10);
    }

    // Test getHotRecommendations - Empty results
    @Test
    void getHotRecommendations_EmptyResults() throws Exception {
        when(recommendationService.getHotRecommendations(16))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/hot"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));

        verify(recommendationService).getHotRecommendations(16);
    }

    // Test getHotRecommendations - Large limit
    @Test
    void getHotRecommendations_LargeLimit() throws Exception {
        when(recommendationService.getHotRecommendations(1000))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/hot")
                .param("limit", "1000"))
                .andExpect(status().isOk());

        verify(recommendationService).getHotRecommendations(1000);
    }

    // Test getHotRecommendations - Service exception
    @Test
    void getHotRecommendations_ServiceException() throws Exception {
        when(recommendationService.getHotRecommendations(16))
            .thenThrow(new RuntimeException("推荐服务异常"));

        mockMvc.perform(get("/recommendations/hot"))
                .andExpect(status().isInternalServerError());

        verify(recommendationService).getHotRecommendations(16);
    }

    // Test getSimilarItems - Success with default limit
    @Test
    void getSimilarItems_Success() throws Exception {
        Long itemId = 1L;
        List<ItemResponse> mockSimilarItems = Arrays.asList(
            createMockItemResponse(2L, "相似商品1", new BigDecimal("90.00")),
            createMockItemResponse(3L, "相似商品2", new BigDecimal("110.00"))
        );

        when(recommendationService.getContentBasedRecommendations(itemId, 4))
            .thenReturn(mockSimilarItems);

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].title").value("相似商品1"))
                .andExpect(jsonPath("$.data[1].title").value("相似商品2"));

        verify(recommendationService).getContentBasedRecommendations(itemId, 4);
    }

    // Test getSimilarItems - Custom limit
    @Test
    void getSimilarItems_CustomLimit() throws Exception {
        Long itemId = 1L;
        List<ItemResponse> mockSimilarItems = Arrays.asList(
            createMockItemResponse(2L, "相似商品", new BigDecimal("90.00"))
        );

        when(recommendationService.getContentBasedRecommendations(itemId, 8))
            .thenReturn(mockSimilarItems);

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId)
                .param("limit", "8"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1));

        verify(recommendationService).getContentBasedRecommendations(itemId, 8);
    }

    // Test getSimilarItems - Empty results
    @Test
    void getSimilarItems_EmptyResults() throws Exception {
        Long itemId = 1L;

        when(recommendationService.getContentBasedRecommendations(itemId, 4))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));

        verify(recommendationService).getContentBasedRecommendations(itemId, 4);
    }

    // Test getSimilarItems - Service exception
    @Test
    void getSimilarItems_ServiceException() throws Exception {
        Long itemId = 1L;

        when(recommendationService.getContentBasedRecommendations(itemId, 4))
            .thenThrow(new RuntimeException("相似度计算异常"));

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId))
                .andExpect(status().isInternalServerError());

        verify(recommendationService).getContentBasedRecommendations(itemId, 4);
    }

    // Test getForYouRecommendations - Without authentication (should return hot recommendations)
    @Test
    void getForYouRecommendations_NotAuthenticated() throws Exception {
        List<ItemResponse> mockRecommendations = Arrays.asList(
            createMockItemResponse(1L, "热门推荐", new BigDecimal("100.00"))
        );

        when(recommendationService.getHotRecommendations(16))
            .thenReturn(mockRecommendations);

        mockMvc.perform(get("/recommendations/for-you"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1));

        verify(recommendationService).getHotRecommendations(16);
    }

    // Test getForYouRecommendations - Custom limit
    @Test
    void getForYouRecommendations_CustomLimit() throws Exception {
        List<ItemResponse> mockRecommendations = Arrays.asList(
            createMockItemResponse(1L, "热门推荐", new BigDecimal("100.00"))
        );

        when(recommendationService.getHotRecommendations(20))
            .thenReturn(mockRecommendations);

        mockMvc.perform(get("/recommendations/for-you")
                .param("limit", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());

        verify(recommendationService).getHotRecommendations(20);
    }

    // Test getForYouRecommendations - With authenticated user (should return personalized recommendations)
    @Test
    @WithMockUser(username = "testuser")
    void getForYouRecommendations_Authenticated() throws Exception {
        List<ItemResponse> mockRecommendations = Arrays.asList(
            createMockItemResponse(1L, "个性化推荐1", new BigDecimal("150.00")),
            createMockItemResponse(2L, "个性化推荐2", new BigDecimal("250.00"))
        );

        // 注意：由于我们使用的是@WithMockUser，我们需要模拟用户ID的获取
        // 在实际的controller中，用户ID是通过CustomUserDetails获取的
        // 这里我们假设用户ID为1L
        when(recommendationService.getItemCFRecommendations(anyLong(), eq(16)))
            .thenReturn(mockRecommendations);

        mockMvc.perform(get("/recommendations/for-you"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        verify(recommendationService).getItemCFRecommendations(anyLong(), eq(16));
    }

    // Test getForYouRecommendations - With authenticated user and custom limit
    @Test
    @WithMockUser(username = "testuser")
    void getForYouRecommendations_AuthenticatedCustomLimit() throws Exception {
        List<ItemResponse> mockRecommendations = Arrays.asList(
            createMockItemResponse(1L, "个性化推荐", new BigDecimal("100.00"))
        );

        when(recommendationService.getItemCFRecommendations(anyLong(), eq(8)))
            .thenReturn(mockRecommendations);

        mockMvc.perform(get("/recommendations/for-you")
                .param("limit", "8"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1));

        verify(recommendationService).getItemCFRecommendations(anyLong(), eq(8));
    }

    // Test triggerSimilarityCalculation - No authentication (should fail)
    @Test
    void triggerSimilarityCalculation_NoAuth() throws Exception {
        mockMvc.perform(post("/recommendations/admin/recalculate"))
                .andExpect(status().isForbidden());

        // Should not call the service without proper authentication
        verify(recommendationService, never()).calculateItemSimilarities();
    }

    // Test boundary conditions
    @Test
    void getHotRecommendations_ZeroLimit() throws Exception {
        when(recommendationService.getHotRecommendations(0))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/hot")
                .param("limit", "0"))
                .andExpect(status().isOk());

        verify(recommendationService).getHotRecommendations(0);
    }

    @Test
    void getHotRecommendations_NegativeLimit() throws Exception {
        when(recommendationService.getHotRecommendations(-1))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/hot")
                .param("limit", "-1"))
                .andExpect(status().isOk());

        verify(recommendationService).getHotRecommendations(-1);
    }

    @Test
    void getSimilarItems_InvalidItemId() throws Exception {
        mockMvc.perform(get("/recommendations/similar/{itemId}", "invalid"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getSimilarItems_NegativeItemId() throws Exception {
        Long itemId = -1L;

        when(recommendationService.getContentBasedRecommendations(itemId, 4))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId))
                .andExpect(status().isOk());

        verify(recommendationService).getContentBasedRecommendations(itemId, 4);
    }

    // Test multiple similar calls
    @Test
    void getSimilarItems_MultipleCalls() throws Exception {
        Long itemId1 = 1L;
        Long itemId2 = 2L;

        List<ItemResponse> similarItems1 = Arrays.asList(
            createMockItemResponse(3L, "相似商品1", new BigDecimal("100.00"))
        );
        List<ItemResponse> similarItems2 = Arrays.asList(
            createMockItemResponse(4L, "相似商品2", new BigDecimal("200.00"))
        );

        when(recommendationService.getContentBasedRecommendations(itemId1, 4))
            .thenReturn(similarItems1);
        when(recommendationService.getContentBasedRecommendations(itemId2, 4))
            .thenReturn(similarItems2);

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId1))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].title").value("相似商品1"));

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId2))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].title").value("相似商品2"));

        verify(recommendationService).getContentBasedRecommendations(itemId1, 4);
        verify(recommendationService).getContentBasedRecommendations(itemId2, 4);
    }

    private ItemResponse createMockItemResponse(Long id, String title, BigDecimal price) {
        ItemResponse response = new ItemResponse();
        response.setId(id);
        response.setTitle(title);
        response.setPrice(price);
        response.setDescription("测试描述");
        return response;
    }
} 